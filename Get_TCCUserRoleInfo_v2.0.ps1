function Get-TCCUserRoleInfo {
    <#
    .SYNOPSIS
    Retrieves comprehensive user information from Active Directory

    .DESCRIPTION
    Gets user account details, group memberships, admin account status, last logon, creation date, and manager information
    Version: 2.0
    Date: 2025-01-15

    .PARAMETER UserName
    The SamAccountName of the user to query

    .EXAMPLE
    Get-TCCUserRoleInfo -UserName "jsmith"

    .EXAMPLE
    gur "jsmith"

    .NOTES
    Alias: gur
    Requires Active Directory PowerShell module
    #>

    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$UserName
    )

    # Define formatting lines
    $line = "----------------------------------------------------"
    $lined = "===================================================="

    try {
        # Single comprehensive query for main user
        $user = Get-ADUser -Identity $UserName -Properties DisplayName, MemberOf, employeeNumber, extensionAttribute9, AccountExpirationDate, Enabled, LastLogonDate, Created, Manager, telephoneNumber, mobile, homePhone, otherTelephone -ErrorAction Stop

        # Check for admin account
        $adminAccount = "a_$UserName"
        $adminUser = Get-ADUser -Filter "SamAccountName -eq '$adminAccount'" -Properties AccountExpirationDate, Enabled, LastLogonDate -ErrorAction SilentlyContinue

        # Get manager information if available
        $managerInfo = $null
        if ($user.Manager) {
            try {
                $managerInfo = Get-ADUser -Identity $user.Manager -Properties DisplayName -ErrorAction SilentlyContinue
            }
            catch {
                # Manager DN might be invalid, continue without error
            }
        }

        # Display basic user information
        Show-UserBasicInfo -User $user -UserName $UserName

        # Display account dates and status
        Show-AccountDetails -User $user

        # Display manager information
        Show-ManagerInfo -ManagerInfo $managerInfo

        # Display phone numbers
        Show-PhoneNumbers -User $user

        # Display admin account information if exists
        if ($adminUser) {
            Show-AdminAccountInfo -AdminUser $adminUser -AdminAccount $adminAccount
        }

        # Display group memberships
        Show-GroupMemberships -User $user

    }
    catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
        Write-Warning "Oops the user '$UserName' was not found!"
    }
    catch {
        Write-Error "An error occurred while retrieving user information: $($_.Exception.Message)"
    }
}

function Show-UserBasicInfo {
    param($User, $UserName)

    $line = "----------------------------------------------------"

    Write-Output $line
    Write-Host "User Name: " -NoNewline
    Write-Host "$($User.DisplayName) " -ForegroundColor Cyan -NoNewline
    Write-Host "`tID: " -ForegroundColor White -NoNewline
    Write-Host "$UserName" -ForegroundColor Green
    Write-Output $line

    Write-Host "Emp Num: " -ForegroundColor White -NoNewline
    if ([string]::IsNullOrEmpty($User.employeeNumber)) {
        Write-Host "NO EMP Num" -ForegroundColor Red -NoNewline
    }
    else {
        Write-Host "$($User.employeeNumber)" -ForegroundColor Cyan -NoNewline
    }

    Write-Host "`t`tPosition: " -ForegroundColor White -NoNewline
    Write-Host "$($User.extensionAttribute9)" -ForegroundColor Cyan
    Write-Host $line
}

function Show-AccountDetails {
    param($User)

    $line = "----------------------------------------------------"

    # Account creation date
    Write-Host "Account Created: " -ForegroundColor White -NoNewline
    if ($User.Created) {
        Write-Host $User.Created.ToString("yyyy-MMM-dd HH:mm") -ForegroundColor Yellow
    }
    else {
        Write-Host "Unknown" -ForegroundColor Red
    }

    # Last logon date
    Write-Host "Last Logon: " -ForegroundColor White -NoNewline
    if ($User.LastLogonDate) {
        $daysSinceLogon = (Get-Date) - $User.LastLogonDate
        Write-Host $User.LastLogonDate.ToString("yyyy-MMM-dd HH:mm") -ForegroundColor Yellow -NoNewline
        Write-Host " (" -ForegroundColor White -NoNewline
        Write-Host "$([math]::Round($daysSinceLogon.TotalDays)) days ago" -ForegroundColor Gray -NoNewline
        Write-Host ")" -ForegroundColor White
    }
    else {
        Write-Host "Never logged in" -ForegroundColor Red
    }

    Write-Host $line

    # Account expiration
    Write-Host "Acc Exp set to: " -ForegroundColor White -NoNewline
    if ($User.AccountExpirationDate) {
        Write-Host $User.AccountExpirationDate.ToString("yyyy-MMM-dd") -ForegroundColor Magenta
    }
    else {
        Write-Host "NO Expire" -ForegroundColor Magenta
    }

    Write-Host $line

    # Account status
    Write-Host "Account status: " -ForegroundColor White -NoNewline
    if ($User.Enabled) {
        Write-Host "ENABLED" -ForegroundColor Green
    }
    else {
        Write-Host "DISABLED" -ForegroundColor Red
    }

    Write-Output $line
}

function Show-ManagerInfo {
    param($ManagerInfo)

    Write-Host "Manager: " -ForegroundColor White -NoNewline
    if ($ManagerInfo) {
        Write-Host "$($ManagerInfo.DisplayName)" -ForegroundColor Cyan -NoNewline
        Write-Host " (" -ForegroundColor White -NoNewline
        Write-Host "$($ManagerInfo.SamAccountName)" -ForegroundColor Green -NoNewline
        Write-Host ")" -ForegroundColor White
    }
    else {
        Write-Host "No manager assigned" -ForegroundColor Yellow
    }

    $line = "----------------------------------------------------"
    Write-Output $line
}

function Show-PhoneNumbers {
    param($User)

    $line = "----------------------------------------------------"
    $hasPhoneNumbers = $false

    # Check if any phone numbers exist
    if ($User.telephoneNumber -or $User.mobile -or $User.homePhone -or $User.otherTelephone) {
        $hasPhoneNumbers = $true
        Write-Host "Phone Numbers:" -ForegroundColor White

        # Office/Work phone
        if ($User.telephoneNumber) {
            Write-Host "  Work Phone: " -ForegroundColor White -NoNewline
            Write-Host "$($User.telephoneNumber)" -ForegroundColor Cyan
        }

        # Mobile phone
        if ($User.mobile) {
            Write-Host "  Mobile: " -ForegroundColor White -NoNewline
            Write-Host "$($User.mobile)" -ForegroundColor Cyan
        }

        # Home phone
        if ($User.homePhone) {
            Write-Host "  Home Phone: " -ForegroundColor White -NoNewline
            Write-Host "$($User.homePhone)" -ForegroundColor Cyan
        }

        # Other phone numbers (can be multiple)
        if ($User.otherTelephone) {
            if ($User.otherTelephone -is [array]) {
                $User.otherTelephone | ForEach-Object {
                    Write-Host "  Other Phone: " -ForegroundColor White -NoNewline
                    Write-Host "$_" -ForegroundColor Cyan
                }
            }
            else {
                Write-Host "  Other Phone: " -ForegroundColor White -NoNewline
                Write-Host "$($User.otherTelephone)" -ForegroundColor Cyan
            }
        }
    }
    else {
        Write-Host "Phone Numbers: " -ForegroundColor White -NoNewline
        Write-Host "No phone numbers on file" -ForegroundColor Yellow
    }

    Write-Output $line
}

function Show-AdminAccountInfo {
    param($AdminUser, $AdminAccount)

    $line = "----------------------------------------------------"
    $lined = "===================================================="

    Write-Host " "
    Write-Host $lined -ForegroundColor Red
    Write-Host "  User has an Admin account " -ForegroundColor Yellow -NoNewline
    Write-Host "$AdminAccount" -ForegroundColor Red
    Write-Host $line -ForegroundColor Red

    # Admin account creation and last logon
    Write-Host "  Admin Last Logon: " -ForegroundColor Yellow -NoNewline
    if ($AdminUser.LastLogonDate) {
        $daysSinceLogon = (Get-Date) - $AdminUser.LastLogonDate
        Write-Host $AdminUser.LastLogonDate.ToString("yyyy-MMM-dd HH:mm") -ForegroundColor Cyan -NoNewline
        Write-Host " (" -ForegroundColor White -NoNewline
        Write-Host "$([math]::Round($daysSinceLogon.TotalDays)) days ago" -ForegroundColor Gray -NoNewline
        Write-Host ")" -ForegroundColor White
    }
    else {
        Write-Host "Never logged in" -ForegroundColor Red
    }

    Write-Host $line -ForegroundColor Red

    # Admin account expiration
    Write-Host "  Acc Exp set to: " -ForegroundColor Yellow -NoNewline
    if ($AdminUser.AccountExpirationDate) {
        Write-Host $AdminUser.AccountExpirationDate.ToString("yyyy-MMM-dd") -ForegroundColor Magenta
    }
    else {
        Write-Host "NO Expire" -ForegroundColor Magenta
    }

    Write-Host $line -ForegroundColor Red

    # Admin account status
    Write-Host "  Admin ACC Status: " -ForegroundColor Yellow -NoNewline
    if ($AdminUser.Enabled) {
        Write-Host "ENABLED" -ForegroundColor Green
    }
    else {
        Write-Host "DISABLED" -ForegroundColor Red
    }

    Write-Host $lined -ForegroundColor Red
}

function Show-GroupMemberships {
    param($User)

    Write-Output ''

    # Get group count and group objects
    $groupsCount = $User.MemberOf.Count
    $groups = $User.MemberOf | ForEach-Object { Get-ADGroup $_ } | Sort-Object Name

    # Display group header
    Write-Host "User is a member of the following groups (" -ForegroundColor White -NoNewline
    Write-Host "$groupsCount" -ForegroundColor Green -NoNewline
    Write-Host "):" -ForegroundColor White
    Write-Host ''

    # Check for required groups
    if (-not ($groups.Name -like "GG-ROLE*")) {
        Write-Host "- GG-ROLE* NOT assigned!!" -ForegroundColor Red
    }
    if (-not ($groups.Name -like "GG-ORG*")) {
        Write-Host "- GG-ORG* not found O: drive NOT assigned!!" -ForegroundColor Red
    }

    # Display groups by category
    $groups | ForEach-Object {
        if ($_.Name -like "GG-ROLE*") {
            Write-Host "Role    : " -ForegroundColor Magenta -NoNewline
            Write-Host "$($_.Name)" -ForegroundColor Green
        }
        elseif ($_.Name -like "GG-ORG*") {
            Write-Host "O Drive : " -ForegroundColor Magenta -NoNewline
            Write-Host "$($_.Name)" -ForegroundColor Green
        }
        elseif ($_.Name -like "GG-APP*") {
            Write-Host "- $($_.Name)" -ForegroundColor Yellow
        }
        else {
            Write-Output "- $($_.Name)"
        }
    }

    Write-Output ""
    Write-Output ""
}

# Set alias for the function
Set-Alias gur Get-TCCUserRoleInfo
