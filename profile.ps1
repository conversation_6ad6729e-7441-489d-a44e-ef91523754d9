#myUtilities
$sso_tools_ver = "1.6.1"
$sso_tools_updated = "07-Feb-2025"
$sso_tools_name = "My tools"
$author = "<PERSON>."
$baseDirectory = "\\fs11-prd\Customer and Digital Services\IT Services\Service Desk\SSO_Tools\"
$sso_tools = $baseDirectory + "sso_tools.ps1"
$pwdFilePath = $baseDirectory + "safe_pwd"
#$sso_tools = "\\fs11-prd\IT Services\Service Desk\Individual folders\Rick Jara\Deploy\sso_tools.ps1"
#$pwdFilePath = "\\fs11-prd\Customer and Digital Services\IT Services\Service Desk\SSO_Tools\"
$line = "----------------------------------------------------"
$title =  "  " + $sso_tools_name + " v" + $sso_tools_ver +" - Updated "+  $sso_tools_updated
write-host $line
write-host $title 
write-host $line
write-host "Type " -ForegroundColor white -NoNewline
write-host "myc " -ForegroundColor green -NoNewline
write-host "at the prompt, to see available tools" -ForegroundColor white
write-host ""
write-host ""

#$pwdFilePath = "\\fs11-prd\IT Services\Service Desk\Individual folders\Rick Jara\Deploy\"
#Import-Module "D:\Users\rxj\Desktop\MyTestModule\MyTestModule.psd1" -Force
#Import-Module "D:\Users\rxj\Documents\WindowsPowerShell\MyModules\SSOTools.psd1" -force

Set-Alias -Name *sso -Value $sso_tools

function Parse-VendorAccountText {
    # Function to parse vendor account text from clipboard and generate formatted output
    [CmdletBinding()]
    param()

    # Get text from the clipboard
    $inputText = Get-Clipboard

    # Parse input text
    $parsedData = @{}
    $inputText -split "`n" | ForEach-Object {
        if ($_ -match "^(.*?):\s*(.*)$") {
            $key = $matches[1].Trim()
            $value = $matches[2].Trim()
            $parsedData[$key] = $value
        }
    }

    # Derive RITM number from TASK number
    $taskNumber = $parsedData["Associated Task"]
    $ritmNumber = $taskNumber -replace "^TASK", "RITM"

    # Generate output text
    $outputText = @"
$ritmNumber - New $($parsedData["Position Title"]) account - $($parsedData["First Name"]) $($parsedData["Last Name"])

New CCTV Vendor account
Associated Ticket: $ritmNumber
First Name       : $($parsedData["First Name"])
Last Name        : $($parsedData["Last Name"])
Company          : $($parsedData["Company"])

Login            : $($parsedData["Login"])
Password         : $($parsedData["Password"])


TCC Email        : $($parsedData["TCC Email"])

End Date         : $($parsedData["End Date"])
"@

    # Return the output text
    $outputText
}

# Example of how to add it to your profile.ps1
# Save this function definition to your $PROFILE script, which can be accessed via:
# notepad $PROFILE

# After adding it, you can use it in any PowerShell session like this:
# Parse-VendorAccountText

Set-Alias pcctv Parse-VendorAccountText

Function Get-LastBootTime {
    param (
        [string]$DeviceName
    )
    
    try {
        $result = Get-CimInstance -ClassName win32_operatingsystem -ComputerName $DeviceName | Select-Object csname, lastbootuptime
        return $result
    }
    catch {
        Write-Error "Unable to connect to $DeviceName. Please ensure the device name is correct and the computer is reachable."
    }
}

# Usage example:
# $Server = "device_name"
# Get-LastBootTime -DeviceName $Server

Set-Alias gpclb Get-LastBootTime

function Compare-TCCUserGroups {
    param (
        [string[]]$Users,
        [switch]$GenerateHtml,
        [switch]$GenerateCsv,
        [int]$ColumnSpacing = 10  # Default column spacing is set to 10
    )

    # Initialize a hashtable to store group memberships and user details
    $UserGroups = @{}
    $UserDetails = @{}

    # Retrieve group memberships and user details for each user
    foreach ($User in $Users) {
        $userObject = Get-ADUser -Identity $User -Properties MemberOf, SamAccountName, DisplayName
        $Groups = $userObject.MemberOf | ForEach-Object { (Get-ADGroup $_).Name }
        $UserGroups[$User] = $Groups
        $UserDetails[$User] = @{
            SamAccountName = $userObject.SamAccountName
            DisplayName = $userObject.DisplayName
        }
    }

    # Combine all groups
    $AllGroups = $UserGroups.Values | ForEach-Object { $_ } | Sort-Object -Unique

    # Determine the length of the longest group name
    $maxGroupNameLength = ($AllGroups | ForEach-Object { $_.Length } | Measure-Object -Maximum).Maximum
    $maxUserNameLength = ($Users | ForEach-Object { $_.Length } | Measure-Object -Maximum).Maximum
    $maxDisplayNameLength = ($UserDetails.Values | ForEach-Object { $_.DisplayName.Length } | Measure-Object -Maximum).Maximum

    # Create header with proper padding for console output
    $header = "GroupName".PadRight($maxGroupNameLength + 5)
    foreach ($User in $Users) {
        $header += $User.PadRight($maxUserNameLength + $ColumnSpacing)
    }
    Write-Host $header

    # Initialize HTML content if GenerateHtml is set
    if ($GenerateHtml) {
        $htmlContent = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AD Group Comparison</title>
    <style>
        body {
            font-family: 'Arial Narrow', Arial, sans-serif; /* Use a condensed font family */
            font-size: 10px; /* Use a smaller font size */
            width: 900px; /* Set the page width to 900 pixels */
            margin: 0 auto; /* Center the page */
        }
        .table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }
        .row {
            display: table-row;
        }
        .cell {
            display: table-cell;
            border: 1px solid black;
            padding: 8px;
        }
        .header .cell {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .group-name {
            width: 400px; /* Set a fixed width for the GroupName column */
        }
        .user-cell {
            width: 150px; /* Set a fixed width for the user columns */
        }
        .different .group-name {
            color: red;
        }
        .member {
            color: green;
        }
        .not-member {
            color: red;
        }
    </style>
</head>
<body>
    <div class="table">
        <div class="row header">
            <div class="cell group-name">GroupName</div>
"@
        foreach ($User in $Users) {
            $htmlContent += "<div class='cell user-cell'>$User</div>`n"
        }
        $htmlContent += "</div>`n"  # Close header row
    }

    $csvData = @()

    # Output the comparison with color and highlight differences in yellow
    foreach ($Group in $AllGroups) {
        $groupMembers = @()
        foreach ($User in $Users) {
            $groupMembers += ($UserGroups[$User] -contains $Group)
        }
        
        $isDifferent = ($groupMembers -contains $true) -and ($groupMembers -contains $false)
        
        if ($isDifferent) {
            Write-Host -NoNewline ($Group.PadRight($maxGroupNameLength + 5)) -ForegroundColor Yellow
            if ($GenerateHtml) {
                $htmlContent += "<div class='row different'><div class='cell group-name'>$Group</div>"
            }
        } else {
            Write-Host -NoNewline ($Group.PadRight($maxGroupNameLength + 5))
            if ($GenerateHtml) {
                $htmlContent += "<div class='row'><div class='cell group-name'>$Group</div>"
            }
        }

        $row = @{ "GroupName" = $Group }

        foreach ($User in $Users) {
            if ($UserGroups[$User] -contains $Group) {
                Write-Host -NoNewline ("✓".PadRight($maxUserNameLength + $ColumnSpacing)) -ForegroundColor Green
                if ($GenerateHtml) {
                    $htmlContent += "<div class='cell user-cell member'>&#x2713;</div>"
                }
                $row[$User] = "✓"
            } else {
                Write-Host -NoNewline ("✗".PadRight($maxUserNameLength + $ColumnSpacing)) -ForegroundColor Red
                if ($GenerateHtml) {
                    $htmlContent += "<div class='cell user-cell not-member'>&#x2717;</div>"
                }
                $row[$User] = "✗"
            }
        }
        $csvData += [pscustomobject]$row
        Write-Host ""  # New line
        if ($GenerateHtml) {
            $htmlContent += "</div>`n"  # Close row
        }
    }

    if ($GenerateHtml) {
        $htmlContent += @"
    </div>
    <div class='table'>
        <div class='row header'>
            <div class='cell'>Supplied User</div>
            <div class='cell'>Display Name</div>
        </div>
"@

        foreach ($User in $Users) {
            $userDetail = $UserDetails[$User]
            $htmlContent += "<div class='row'><div class='cell'>$($User)</div><div class='cell'>$($userDetail.DisplayName)</div></div>`n"
        }

        $htmlContent += @"
    </div>
</body>
</html>
"@

        # Save the HTML content to a file
        $htmlFilePath = "D:\AD_Group_Comparison.html"
        $htmlContent | Out-File -FilePath $htmlFilePath -Encoding UTF8

        Write-Host "HTML report generated and saved to $htmlFilePath"
    }

    if ($GenerateCsv) {
        # Create the CSV report
        $csvFilePath = "D:\AD_Group_Comparison.csv"
        $csvData | Export-Csv -Path $csvFilePath -NoTypeInformation -Encoding UTF8

        Write-Host "CSV report generated and saved to $csvFilePath"
    }

    # Output the user details at the end
    Write-Host "`nUser Details:"
    Write-Host "Supplied User".PadRight($maxUserNameLength + $ColumnSpacing) + "Display Name"
    foreach ($User in $Users) {
        $userDetail = $UserDetails[$User]
        Write-Host $User.PadRight($maxUserNameLength + $ColumnSpacing) + $userDetail.DisplayName
    }
}

Set-Alias cug Compare-TCCUserGroups

function Highlight-Text {
    param(
        [string]$text,
        [string]$searchString1,
        [string]$searchString2,
        [string]$highlightColor1 = "Cyan",
        [string]$highlightColor2 = "Green"
    )

    # Split the text into parts based on the search strings
    $parts = $text -split "($searchString1)|($searchString2)"

    # Output each part, highlighting the search strings
    foreach ($part in $parts) {
        if ($part -eq $searchString1) {
            Write-Host $part -ForegroundColor $highlightColor1 -NoNewline
        }
        elseif ($part -eq $searchString2) {
            Write-Host $part -ForegroundColor $highlightColor2 -NoNewline
        }
        else {
            Write-Host $part -NoNewline
        }
    }
}

<#
function Format-DateProperties {
    param (
        [Parameter(Mandatory=$true)]
        [PSObject[]]$users,

        [Parameter(Mandatory=$true)]
        [string[]]$properties
    )

    foreach ($user in $users) {
        foreach ($property in $properties) {
            if ($user.PSObject.Properties[$property] -and ($user.$property -is [datetime])) {
                $user.$property = $user.$property.ToString("dd-MMMM-yyyy HH:mm")
            }
        }
    }

    return $users
}
#>

function Format-DateProperties {
    param (
        [Parameter(Mandatory=$true)]
        [PSObject[]]$users,

        [Parameter(Mandatory=$true)]
        [string[]]$properties
    )

    foreach ($user in $users) {
        foreach ($property in $properties) {
            if ($user.PSObject.Properties[$property] -and ($user.$property -is [datetime])) {
                # Create a new property name for the formatted date
                $formattedPropertyName = "Formatted_$property"
                
                # Check if the formatted property already exists
                if ($user.PSObject.Properties[$formattedPropertyName]) {
                    # Update the existing property
                    $user.PSObject.Properties[$formattedPropertyName].Value = $user.$property.ToString("dd-MMMM-yyyy HH:mm")
                } else {
                    # Add the new property with the formatted date
                    $user | Add-Member -MemberType NoteProperty -Name $formattedPropertyName -Value $user.$property.ToString("dd-MMMM-yyyy HH:mm")
                }
            }
        }
    }

    return $users
}

function Get-SmtpEntries {
    param (
        [string]$username  # Parameter to accept the username
    )

    # Import the Active Directory module
    Import-Module ActiveDirectory

    # Get the user object from Active Directory
    $user = Get-ADUser -Identity $username -Properties proxyAddresses, targetAddress

    # Filter the proxyAddresses property to find entries that start with "SMTP:" or "smtp:"
    $smtpEntries = $user.proxyAddresses | Where-Object { $_ -like "SMTP:*" -or $_ -like "smtp:*" }

    # Print each SMTP entry on a separate line
    Write-Host ""
    foreach ($entry in $smtpEntries) {
        Write-Output $entry
    }
    Write-Host ""
        # Print the targetAddress property
    Write-Output "Target Address: $($user.targetAddress)"
    Write-Host ""
}

Set-Alias gusmtp Get-SmtpEntries

function Ultimate-ADUserSearch {
    <#
    .SYNOPSIS
    PowerShell 5.1 compatible Active Directory user search
    
    .DESCRIPTION
    Fast and reliable AD user search optimized for PowerShell 5.1
    Version: 3.27-PS5.1
    #>
    
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$false)]
        [string]$substring1,

        [Parameter(Mandatory=$false)]
        [string]$substring2,

        [Parameter(Mandatory=$false)]
        [string[]]$PropertiesToAdd,

        [Parameter(Mandatory=$false)]
        [string[]]$PropertyToList,

        [Parameter(Mandatory=$false)]
        [string]$SortBy,

        [Parameter(Mandatory=$false)]
        [switch]$ReplaceProperties,

        [Parameter(Mandatory=$false)]
        [switch]$Beg,

        [Parameter(Mandatory=$false)]
        [switch]$End,

        [Parameter(Mandatory=$false)]
        [switch]$help,

        [Parameter(Mandatory=$false)]
        [switch]$showFilter,

        [Parameter(Mandatory=$false)]
        [switch]$UseOr,

        [Parameter(Mandatory=$false)]
        [switch]$useCol,

        [Parameter(Mandatory=$false)]
        [switch]$ShowEnabled,

        [Parameter(Mandatory=$false)]
        [switch]$ShowDisabled,

        [Parameter(Mandatory=$false)]
        [switch]$ExactMatch
    )

    # Display simple help
    if ($help) {
        Write-Host @"
Ultimate-ADUserSearch (v3.27-PS5.1) - AD User Search
----------------------------------------------------
Basic usage: *us -substring1 "name" [-substring2 "name2"]
Advanced:    *us -substring1 "a" -PropertiesToAdd Department -ShowEnabled
"@
        return
    }

    # Validate parameters
    if (-not $substring1 -and -not $substring2) {
        Write-Warning "You must specify at least one search term"
        return
    }

    # Define core properties with null checks
    $defaultProperties = @("GivenName", "SurName", "SamAccountName")
    $defaultPropertiesToList = @("Enabled", "LastLogonDate", "Created", "AccountExpires")

    $searchProperties = if ($ReplaceProperties -and $PropertiesToAdd) {
        $PropertiesToAdd | Where-Object { $_ }
    } else {
        if ($PropertiesToAdd) {
            $defaultProperties + ($PropertiesToAdd | Where-Object { $_ })
        } else {
            $defaultProperties
        }
    }

    $outputProperties = if ($PropertyToList) {
        $defaultPropertiesToList + ($PropertyToList | Where-Object { $_ })
    } else {
        $defaultPropertiesToList
    }

    # Build filter safely
    $filterParts = @()
    foreach ($term in @($substring1, $substring2)) {
        if (-not [string]::IsNullOrWhiteSpace($term)) {
            $safeTerm = $term.Replace("'", "''")
            $wildcard = if ($ExactMatch) { "'$safeTerm'" }
                       elseif ($Beg) { "'$safeTerm*'" }
                       elseif ($End) { "'*$safeTerm'" }
                       else { "'*$safeTerm*'" }
            
            $conditions = @()
            foreach ($prop in $searchProperties) {
                if (-not [string]::IsNullOrEmpty($prop)) {
                    $conditions += "$prop -like $wildcard"
                }
            }
            if ($conditions.Count -gt 0) {
                $filterParts += "(" + ($conditions -join " -or ") + ")"
            }
        }
    }

    # Combine filter parts
    if ($filterParts.Count -eq 0) {
        $filter = "(objectClass=user)"
    }
    elseif ($filterParts.Count -eq 1) {
        $filter = $filterParts[0]
    }
    else {
        $operator = if ($UseOr) { " -or " } else { " -and " }
        $filter = $filterParts[0] + $operator + $filterParts[1]
    }

    # Add account status filter
    if ($ShowEnabled) { $filter = "(&($filter)(Enabled=TRUE))" }
    elseif ($ShowDisabled) { $filter = "(&($filter)(Enabled=FALSE))" }

    # Show filter if requested
    if ($showFilter) { Write-Host "[Filter] $filter" }

    # Get properties ensuring no nulls
    $allProperties = @($searchProperties + $outputProperties) | 
                    Where-Object { $_ } |
                    Select-Object -Unique

    # Execute query with error handling
    try {
        $users = Get-ADUser -Filter $filter -Properties $allProperties -ErrorAction Stop |
                 Select-Object *, @{
                    Name = 'LastLogonFormatted'
                    Expression = { if ($_.LastLogonDate) { $_.LastLogonDate.ToString("yyyy-MMM-dd") } else { "Never" } }
                 }, @{
                    Name = 'CreatedFormatted'
                    Expression = { $_.Created.ToString("yyyy-MMM-dd") }
                 }, @{
                    Name = 'AccountExpiresFormatted'
                    Expression = {
                        if ($_.AccountExpires -le 0 -or $_.AccountExpires -gt [DateTime]::MaxValue.Ticks) {
                            "Never"
                        } else {
                            [DateTime]::FromFileTime($_.AccountExpires).ToString("yyyy-MMM-dd")
                        }
                    }
                 }
    } catch {
        Write-Error "AD query failed: $_"
        return
    }

    # Handle no results
    if (-not $users) {
        Write-Host "No users found matching criteria" -ForegroundColor Yellow
        return
    }

    # Sort results
    $sortedUsers = if ($SortBy) { $users | Sort-Object $SortBy } else { $users | Sort-Object SurName }

    # Prepare output properties safely
    $displayProperties = @($searchProperties + @(
        'LastLogonFormatted',
        'CreatedFormatted', 
        'AccountExpiresFormatted',
        'Enabled'
    )) | Where-Object { $_ }

    # Display results
    $finalOutput = $sortedUsers | Select-Object $displayProperties
    if ($useCol -and (Get-Command Highlight-Text -ErrorAction SilentlyContinue)) {
        $searchTerms = @($substring1, $substring2) | Where-Object { $_ }
        $finalOutput | Out-String | Highlight-Text -searchTerms $searchTerms
    } else {
        $finalOutput | Format-Table -AutoSize
    }

    # Show count
    Write-Host "`nFound $($users.Count) users" -ForegroundColor Green
}
Set-Alias -Name *us -Value Ultimate-ADUserSearch -Force

function Get-ADUserByPartName {
    param (
        [Parameter(Mandatory=$false)]
        [string]$PartName,
        [Parameter(Mandatory=$false)]
        [string]$SecondPartName,
        [Parameter(Mandatory=$false)]
        [switch]$Help
    )

    if ($Help) {
        Write-Host "Usage: Get-ADUserByPartName -PartName <part of name> [-SecondPartName <second part of name>] [-Help]"
        Write-Host "Searches for AD users where the SamAccountName, Name, or UserPrincipalName contains the specified part of the name."
        Write-Host "If a second part of the name is specified, it further refines the search."
        Write-Host "The -Help switch provides this help information."
        return
    }

    $filter = "SamAccountName -like '*$PartName*' -or Name -like '*$PartName*' -or UserPrincipalName -like '*$PartName*'"

    if ($SecondPartName) {
        $filter2 = "SamAccountName -like '*$SecondPartName*' -or Name -like '*$SecondPartName*' -or UserPrincipalName -like '*$SecondPartName*'"
        $filter = "(" + $filter + ") -and (" + $filter2 + ")"
    }

    Get-ADUser -Filter $filter -Property * |
        Select-Object -Property @{
            Name='ID';
            Expression={$_.SamAccountName}
        }, @{
            Name='GivenName';
            Expression={$_.GivenName}
        }, @{
            Name='SurName';
            Expression={$_.SurName}
        }, @{
            Name='DisplayName';
            Expression={$_.DisplayName}
        }, @{
            Name='UserPrincipalName';
            Expression={$_.UserPrincipalName}
        }, @{
            Name='Employee Num';
            Expression={$_.EmployeeNumber}
        }, @{
            Name='Enabled';
            Expression={$_.Enabled}
        }, @{
            Name='Expiry Date';
            Expression={
                if ($_.AccountExpirationDate) {
                    $_.AccountExpirationDate.ToString("dd-MMM-yyyy")
                } else {
                    "No Expiry"
                }
            }
        }, @{
            Name='LastLogOn';
            Expression={
                if ($_.LastLogonDate) {
                    $_.LastLogonDate.ToString("dd-MMM-yyyy")
                } else {
                    "NO Logon"
                }
            }
        }, @{
            Name='LockedOut';
            Expression={if ($_.LockedOut -eq $true) { "YES" } else { "NO" }}
        }, @{
            Name='Position';
            Expression={if ([string]::IsNullOrEmpty($_.extensionAttribute9)) { "No Data" } else { $_.extensionAttribute9 }}
        } | Sort-Object GivenName | Format-Table
}

set-alias *su Get-ADUserByPartName

function Check-OSDFinalFileExits {
    [CmdletBinding()]
    param (
        [Parameter(ValueFromPipeline=$true, ValueFromPipelineByPropertyName=$true)]
        [Alias('host')]
        [string[]]$ComputerName,
        [switch]$List  # New switch parameter
    )

    process {
        foreach ($computer in $ComputerName) {
            try {
                # Check if the computer is online
                $pingResult = Test-Connection -ComputerName $computer -Count 1 -Quiet

                if ($pingResult) {
                    # Construct the UNC path to the file
                    $filePath = "\\$computer\c$\SOE\OSD-FINAL_TS_*"

                    # Check if the file exists
                    $fileExists = Test-Path -Path $filePath

                    if ($fileExists) {
                        # Get the full file name
                        $fullFileName = Get-ChildItem -Path $filePath | Select-Object -ExpandProperty FullName
                        Write-Host "File found on $computer $($fullFileName)" -ForegroundColor Green
                    } else {
                        # Print the computer name in red
                        Write-Host -ForegroundColor Red "File not found on $computer"
                        if ($List) {
                            # List folder contents if requested
                            $folderContents = Get-ChildItem "\\$computer\c$\SOE\*.log"
                            Write-Host "Contents of C:\SOE on $computer"
                            $folderContents | ForEach-Object {
                                Write-Host "  $_" -ForegroundColor Yellow
                            }
                        }
                    }
                } else {
                    Write-Host -ForegroundColor Red "Computer $computer is offline (Check that you have typed the corrent asset and the correct designation ie NB,TB etc)"
                }
            } catch {
                Write-Host "Error occurred while checking $computer $_"
            }
        }
    }
}

Set-Alias cfinal Check-OSDFinalFileExits

function Write-HostColored {
    param (
        [string]$Text
    )

    $colorPattern = "#(\w+)(?::(\w+))?#"

    $coloredText = $Text -replace $colorPattern, {
        $foregroundColor = $matches[1]
        $backgroundColor = $matches[2]

        $ansiColorCodes = @{
            'black'   = 30
            'red'     = 31
            'green'   = 32
            'yellow'  = 33
            'blue'    = 34
            'magenta' = 35
            'cyan'    = 36
            'white'   = 37
        }

        $ansiEscape = [char]27 + '['
        $resetEscape = "${ansiEscape}0m"

        $colorArgs = @()
        if ($foregroundColor) {
            $colorArgs += "${ansiEscape}$($ansiColorCodes[$foregroundColor])m"
        }
        if ($backgroundColor) {
            $colorArgs += "${ansiEscape}$($ansiColorCodes[$backgroundColor])m"
        }

        $coloredSubstring = $matches[3]
        "$colorArgs$coloredSubstring$resetEscape"
    }

    Write-Host $coloredText
}




function Reload-Console {
    clear
    Write-Host "Reload Console"
    Get-Process -Id $PID | Select-Object -ExpandProperty Path | ForEach-Object {
        Invoke-Command { & "$_" } -NoNewScope
    }
}

New-Alias *r Reload-Console

function Initialize-TCCUserPassword {
	[CmdletBinding()]

	param(
		[string]$TextToUse # NOTE: this must fit the password requirements
	)

	begin {
	}

	process {
		if ($TextToUse -ne '') {
			$password = $TextToUse
		} else {
			Write-Verbose "Get the path to the passwords file"
			$words = Get-Content "$pwdFilePath\SafeWords.txt"
			Write-Verbose "Get the correct culture to capitalize the text"
			$textformat = (Get-Culture).TextInfo
			$password1 = $textformat.ToTitleCase($words[(Get-Random -Maximum $words.Count -Minimum 0)])
			Write-Verbose "Word 1 is $password1"
			$number = (Get-Random -Minimum 1 -Maximum 100).ToString("00")
			Write-Verbose "Number is set to $number"
			$password2 = $textformat.ToTitleCase($words[(Get-Random -Maximum $words.Count -Minimum 0)])
			Write-Verbose "Word 2 is $password2"
			$password = "$password1$number$password2"
		}
        
		#$returnvalue = @{ 'TextRepresentation' = $password;
		#	'SecureRepresentation' = ConvertTo-SecureString $password -AsPlainText -Force }
		#New-Object -TypeName PSObject -Prop $returnvalue
        write-host "`nPassword: " -NoNewline
        write-host "$password`n" -ForegroundColor Green
        #return "`n$password`n"
        #write-host ""
	}

	end {
}
}

Set-Alias *gpwd Initialize-TCCUserPassword

<#

####################################################################################################
#   
#   Script Language:        PowerShell
#   Script Name:            "Find-TCCNextUserLogon.ps1"
#   Purpose:                Exposes a function to return the next available user logon given the parameters specified
#   Author:                 David House
#   Company:                Townsville City Council
#   Git Repository:         Git repository link
#   
####################################################################################################

#>

function Find-TCCNextUserLogon {
  [CmdletBinding()]
  param (
    [Parameter(Mandatory = $true)]
    [string]$GivenName,
  
    [Parameter(Mandatory = $true)]
    [string]$Surname
  )

  begin {
    $FillerCharacters = [char[]]([char]'z'..[char]'a') + (0..9)
    $FirstInitial = $GivenName.Substring(0, 1)
    $SecondInitial = $Surname.Substring(0, 1)
    $Patterns = @(
    ('{0}{1}{2}' -f $FirstInitial, '{0}', $SecondInitial).ToLower(),
    ('{0}{1}{2}' -f $FirstInitial, $SecondInitial, '{0}').ToLower(),
    ('{0}{1}{2}' -f $FirstInitial, '{0}', '{1}').ToLower()
    )
    $LogonToUse = ''
    $Attempts = 0
  }

  process {
    foreach ($Pattern in $Patterns) {
      foreach ($FillerCharacter in $FillerCharacters) {
        if ($Pattern -notlike '*{1}*') {
          $NewLogon = $Pattern -f $FillerCharacter
          $ExistingUser = Get-ADUser -Filter "SamAccountName -eq '$NewLogon'"
  
          if (-not $ExistingUser) {
            $LogonToUse = $NewLogon
            break
          }

          $Attempts += 1
          Write-Verbose "$NewLogon has already been used. (Attempts: $Attempts)"
        } else {
          foreach ($FillerCharacter2 in $FillerCharacters) {
            $NewLogon = $Pattern -f $FillerCharacter2, $FillerCharacter
            $ExistingUser = Get-ADUser -Filter "SamAccountName -eq '$NewLogon'"
    
            if (-not $ExistingUser) {
              $LogonToUse = $NewLogon
              break
            }

            $Attempts += 1
            Write-Verbose "$NewLogon has already been used. (Attempts: $Attempts)"
          }

          if ($LogonToUse) {
            break
          }
        }
      }
    
      if ($LogonToUse) {
        break
      }
    }

    if (-not $LogonToUse) {
      Write-Verbose "No available logon has been found. (Attempts: $Attempts)"
      return $null
    } else {
      Write-Verbose "$LogonToUse has been found as the next available logon. (Attempts: $Attempts)"
      #return "`nNew User ID for $GivenName $SurName is: $LogonToUse`n"
      write-host "`nNew User ID for " -ForegroundColor White -NoNewline
      write-host "$GivenName $SurName " -NoNewline -ForegroundColor Yellow
      write-host " is: " -ForegroundColor White -NoNewline
      write-host "$LogonToUse`n" -ForegroundColor Green
    }
  }

  end {
  
  }
}

Set-Alias *gnu Find-TCCNextUserLogon

function Highlight-Substring {
    param (
        [string]$inputString,
        [string]$substring
    )

    # Find the index of the substring
    $startIndex = $inputString.IndexOf($substring)

    if ($startIndex -ge 0) {
        # Extract string1 and string2
        $string1 = $inputString.Substring(0, $startIndex)
        $string2 = $inputString.Substring($startIndex + $substring.Length)

        # Display string1 in white, the substring in red, and string2
        Write-Host $string1 -ForegroundColor White -NoNewline
        Write-Host $substring -ForegroundColor Red -NoNewline
        Write-Host $string2 -ForegroundColor White 
    }
    else {
        Write-Host "Substring not found in the input string."
    }
}

Set-Alias hsub Highlight-Substring


function Search-ADUserWildcard {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [string]$searchString
    )

    # Perform the search on AD users
    $users = Get-ADUser -Filter "Name -like '*$searchString*' -or GivenName -like '*$searchString*' -or SamAccountName -like '*$searchString*'" -Properties GivenName, Surname, SamAccountName, Enabled, AccountExpirationDate

 
    function Write-Highlight {
        param (
            [string]$text,
            [string]$search
        )

        $parts = $text -split ($search, [System.StringSplitOptions]::None)
        for ($i = 0; $i -lt $parts.Count; $i++) {
            Write-Host $parts[$i] -NoNewline
            if ($i -lt $parts.Count - 1) {
                Write-Host $search -ForegroundColor Red -NoNewline
                # Write-Host $search -ForegroundColor Red
            }
        }
        # Write-Host " " -NoNewline
        # Write-Host " " 
    }


    # function Write-Highlight {
    #     param (
    #         [string]$text,
    #         [string]$search
    #     )
    
    #     $result = ""
    #     $parts = $text -split ($search, [System.StringSplitOptions]::None)
    #     for ($i = 0; $i -lt $parts.Count; $i++) {
    #         $result += $parts[$i]
    #         if ($i -lt $parts.Count - 1) {
    #             $result += $search
    #         }
    #     }
    #     return $result
    # }
    
    function Write-Line {
        param(
            [string]$label,
            [string]$value
        )

        Write-Host  $label -ForegroundColor Cyan -NoNewline
        Write-Host  $value`t -ForegroundColor Green
    }

    # Display the results
    Write-Host ""
    $line = "------------------------------------------------------------------------------------------------------------"
    foreach ($user in $users) {
        Write-Host $line
        # Write-Host "[Name: " -NoNewline -ForegroundColor cyan
        # Write-Highlight -text $user.Name -search $searchString
        # Write-Host "]`tGiven Name: " -NoNewline -ForegroundColor cyan
        # Write-Highlight -text $user.GivenName -search $searchString
        # Write-Host "`tSAM Account Name: " -NoNewline -ForegroundColor cyan
        # Write-Highlight -text $user.SamAccountName -search $searchString\

        Write-Host "Given Name: " -NoNewline -ForegroundColor cyan
        Write-Highlight -text $user.GivenName -search $searchString 
        Write-Host "`tSurname: " -NoNewline -ForegroundColor cyan 
        Write-Highlight -text $user.Surname -search $searchString
        Write-Host "`tID: " -NoNewline -ForegroundColor cyan
        Write-Highlight -text $user.SamAccountName -search $searchString
        Write-Host " "
        $status = if ($user.Enabled -eq $true) { "Active" } else { "Inactive" }

        #write-host ""
        Write-Host "Account Status: " -ForegroundColor cyan -NoNewline
        if ($status -eq "Inactive")
        { 
            write-host $status -ForegroundColor red -NoNewline
        } else {
            write-host $status -ForegroundColor green -NoNewline
        }

        # write-host ""
        # Write-Host "Account Status: " -ForegroundColor cyan -NoNewlineS
        # if ($status -eq "Inactive")
        # { 
        #     write-host $status -ForegroundColor red -NoNewline
        # } else {
        #     write-host $status -ForegroundColor green -NoNewline
        # }


        # Check for account expiration date
        $expiry = if ($user.AccountExpirationDate -ne $null) { $user.AccountExpirationDate.ToString("yyyy-MM-dd") } else { "No Expiry" }
       
            
        Write-Host "`tExpiry Date : " -ForegroundColor cyan -NoNewline
        write-host $expiry -foregroundColor green
        Write-host " "    
        $extraAtt = Get-ADUser $user -Properties EmployeeNumber, whenCreated, whenChanged, LastLogonDate, LockedOut
        $lockedout = if ($extraAtt.LockedOut -eq $true) { "YES" } else { "NO" }
        Write-Line -Label "Emp Number       : " -Value $extraAtt.EmployeeNumber
        Write-Line -Label "Account Created  : " -Value $extraAtt.whenCreated
        Write-Line -Label "Last Logon date  : " -Value $extraAtt.LastLogonDate
        write-host " "
        Write-Line -Label "Account Changed  : " -Value $extraAtt.whenChanged
        Write-Line -Label "Locked out       : " -Value $lockedout
        write-host " "
        # Write-Host "---------------------------------------------------------------------"
    }
    write-host $line
    write-host ""
}


set-alias *sbs Search-ADUserWildcard

function Get-UserByString {
    param (
        [string]$searchString
    )

    # Retrieve all user objects
    $allUsers = Get-ADUser -Filter *

    # Filter users containing the specified string in Name, GivenName, or SamAccountName
    $matchingUsers = $allUsers | Where-Object {
        $_.Name -like "*$searchString*" -or
        $_.GivenName -like "*$searchString*" -or
        $_.SamAccountName -like "*$searchString*"
    }

    # Display only Name and SamAccountName for matching users
    foreach ($user in $matchingUsers) {
        $highlightedName = $user.Name -replace $searchString, "[$searchString]"
        $highlightedSamAccountName = $user.SamAccountName -replace $searchString, "[$searchString]"

        Write-Host "Name: $highlightedName, SamAccountName: $highlightedSamAccountName"
    }
}

# Example usage:
#Get-UserByString -searchString "john"
Set-Alias *sus Get-UserByString


function Set-And-Disable-ADAccount {
# usge Set-And-Disable-ADAccount -Identity User -ExpirationDate "31/12/2023"
    param (
        [Parameter(Mandatory=$true)]
        [string]$Identity,
        [Parameter(Mandatory=$true)]
        [datetime]$ExpirationDate
    )
    Set-ADAccountExpiration -Identity $Identity -DateTime $ExpirationDate
    Disable-ADAccount -Identity $Identity
}

Set-Alias *sdda Set-And-Disable-ADAccount

function Load-SSOProfile {
#Get-Alias | ForEach-Object { Remove-Item -Path Alias:\$_ }
$myToolsScript = "\\fs11-prd\IT Services\Service Desk\Individual folders\Rick Jara\Deploy\profile.ps1"
. $myToolsScript
}

set-alias *lssop Load-SSOProfile

function Backup-MyProfile {
    [CmdletBinding()]
    param (
        [switch]$Reverse,
        [string]$Version = "v1"
    )
    $source = "D:\Users\a_rxj\Documents\WindowsPowerShell\profile.ps1"
    $destination = "\\fs02-prd\IT Services\Service Desk\Individual folders\Rick Jara\myPSProfile\profile_$Version.ps1"
    if ($Reverse) {
        Copy-Item -Path $destination -Destination $source -Force
    }
    else {
        $TimeStamp = (Get-Date).ToString('MMddyyyyhhmmss')
        $versioned_destination = "\\fs02-prd\IT Services\Service Desk\Individual folders\Rick Jara\myPSProfile\profile_$Version-$TimeStamp.ps1"
        Copy-Item -Path $source -Destination $versioned_destination -Force
    }
}

set-alias *bak Backup-MyProfile

#$sso_tools = "\\fs11-prd\IT Services\Service Desk\Individual folders\Rick Jara\Deploy\sso_tools.ps1"
$sso_profile = "D:\Users\a_rxj\Documents\WindowsPowerShell\user_script\profile.ps1"

function Sig-SSOTools{
    Invoke-CodeSigning $sso_tools
}

set-alias *sssot Sig-SSOTools

function Sig-SSOProfile{
    Invoke-CodeSigning $sso_profile
}

set-alias *sssop Sig-SSOProfile


function Get-AdminConsole {
    Start-Process powershell -Credential (Get-Credential) -Verb runAs
}

set-alias gac Get-AdminConsole

function Get-OSTFileSize {
    param (
        [string]$ComputerName,
        [string]$UserName
    )
    $OSTPath = "\\$ComputerName\c$\Users\$UserName\AppData\Local\Microsoft\Outlook"
    $OSTFiles = Get-ChildItem $OSTPath -Recurse -Filter *.ost
    $OSTFiles | ForEach-Object {
        [PSCustomObject]@{
            'File Name' = $_.Name
            'Size (MB)' = '{0:N2}' -f ($_.Length / 1MB)
        }
    }
}

set-alias ostz Get-OSTFileSize

function back-my-profile {
    #$targerpath = "\\fs02-prd\IT Services\Service Desk\Individual folders\Rick Jara\pspb\"
    $targetpath = "D:\Users\rxj\OneDrive - TOWNSVILLE CITY COUNCIL\pspb\"
    $LocalProfile = Get-TCCPP
    $path = Split-Path -Path $LocalProfile -Parent
    $file = Split-Path -Path $LocalProfile -Leaf
    $name = [System.IO.Path]::GetFileNameWithoutExtension($file)
    $Backup = $Targetpath+"\"+$name+"_back.ps1"
    $file1 = Get-ChildItem -Path $LocalProfile
    $ext = "_backup.ps1"
    #$ext = "_backup_test.ps1"
    #$Pdate = $file1.LastWriteTime

    $outprofile = $Targetpath+$name+$ext

    #Write-Host "2" $file
    #write-host "3" $Backup
    #write-host "4" $file1
    #write-host "5" $Pdate
    #write-host "6" $outprofile
    Copy-Item $LocalProfile $outprofile
    
}

Set-Alias bp back-my-profile

function Get-ConsoleColors {
    [Enum]::GetValues([System.ConsoleColor]) | ForEach-Object {
        Write-Host $_ -ForegroundColor $_
    }
}

Set-Alias gcc Get-ConsoleColors

function Get-Status {
    param (
        [bool]$inputBoolean
    )

    if ($inputBoolean) {
        return "ENABLED"
    } else {
        return "DISABLED"
    }
}

set-alias *cc Get-ConsoleColors

#<#
function Get-TCCUserRoleInfo {
# <>
# ---------------------------------------------------- #
# Get-TCCUserRoleInfo                                  #
# Version 1.3  17-04-2024                              #
# Retrives users basic acc info and group memberships  #
# Alias: gur                                           #
# Usage: gur abc                                       #
# ---------------------------------------------------- #
# </>
    param (
        [Parameter(Mandatory = $true)]
        [string]$UserName
    )
    #Clear-Host
    # Retrieve the user object
    $line =  "----------------------------------------------------"
    $lined = "===================================================="

    try {
    $admin_account = "a_" + $UserName
    $user = Get-ADUser -Identity $UserName -Properties DisplayName, MemberOf
    $a_user = Get-ADUser -Filter { SamAccountName -eq $admin_account }
    $groups_count = (Get-ADUser $UserName -Properties MemberOf).MemberOf.count
    $empnum = Get-ADUser -Identity $UserName -Properties employeeNumber | Select-Object -ExpandProperty employeeNumber
    $emp_pos = (Get-ADUser -Filter { SamAccountName -eq $Username } -Properties extensionAttribute9).extensionAttribute9
    $ExDate = Get-ADUser -Identity $UserName -Properties AccountExpirationDate | Select-Object -ExpandProperty AccountExpirationDate | ForEach-Object {$_.ToString('D')}

    #$accstatus = Get-ADUser -Identity $UserName | Select-Object -Property Enabled | Format-Table -HideTableHeaders
    $accstatus = (Get-ADUser -Identity $UserName | Select-Object -Property Enabled | Format-Table -HideTableHeaders | Out-String).Trim()
    #$adminstatus = (Get-ADUser -Identity $admin_account | Select-Object -Property Enabled | Format-Table -HideTableHeaders | Out-String).Trim()

    # Display the user's display name property
    Write-Output $line
    Write-host "User Name: " -NoNewline
    write-Host "$($user.DisplayName) " -ForegroundColor Cyan -NoNewline
    write-Host "`tID: " -ForegroundColor white -NoNewline
    # write-host "'" -ForegroundColor cyan -NoNewline
    write-host "$UserName" -ForegroundColor green
    # write-host "' " -ForegroundColor cyan
    Write-Output $line
    write-host "Emp Num: " -ForegroundColor white -NoNewline
    
    if([string]::IsNullOrEmpty($empnum))
    {
        Write-Host "NO EMP Num" -ForegroundColor Red -NoNewline
    }
    else
    {
        write-Host "$empnum" -ForegroundColor cyan -NoNewline
    }

    Write-Host "`t`tPosition: " -ForegroundColor White -NoNewline
    Write-Host "$emp_pos" -ForegroundColor cyan 

    Write-host $line 


    write-Host "Acc Exp set to: " -ForegroundColor white -NoNewline
    if([string]::IsNullOrEmpty($ExDate))
    {
        Write-Host "NO Expire" -ForegroundColor magenta
    }
    else
    {
        write-Host $ExDate -ForegroundColor magenta
    }
    # Write-Output $line
    Write-host $line 
    write-host "Account status: "  -ForegroundColor white -NoNewline
    # $status = Get-Status $accstatus 
    # Write-host $status

    if ($accstatus -eq "False")
    {
        Write-Host "DISSABLED" -ForegroundColor red
    }
    elseif ($accstatus -eq "True")
    {
        Write-Host "ENABLED" -ForegroundColor green
    }

    Write-Output $line

    if ($a_user) {

        $adminstatus = (Get-ADUser -Identity $admin_account | Select-Object -Property Enabled | Format-Table -HideTableHeaders | Out-String).Trim()
        $adminExDate = Get-ADUser -Identity $admin_account -Properties AccountExpirationDate | Select-Object -ExpandProperty AccountExpirationDate | ForEach-Object {$_.ToString('D')}
        Write-Host " "
        Write-host $lined -ForegroundColor Red 
        Write-host "  User has an Admin account " -ForegroundColor yellow -NoNewline
        write-host "$admin_account" -ForegroundColor red -NoNewline
        write-host " "
        Write-host $line -ForegroundColor Red 

        # $a_status = Get-Status $adminstatus
        write-Host "  Acc Exp set to: " -ForegroundColor yellow -NoNewline
        if([string]::IsNullOrEmpty($adminExDate))
        {
            Write-Host "NO Expire" -ForegroundColor magenta
        }
        else
        {
            write-Host $adminExDate -ForegroundColor magenta
        }
        Write-host $line -ForegroundColor Red 
        write-host "  Admin ACC Status: " -ForegroundColor Yellow -NoNewline
        if ( $adminstatus -eq "False")
            {
                Write-Host "DISSABLED" -ForegroundColor red
            }
            elseif ($accstatus -eq "True")
            {
                Write-Host "ENABLED" -ForegroundColor green
            }
        #write-host $adminstatus -ForegroundColor Red
        Write-host $lined -ForegroundColor Red 
    } else {
        # Write-Output "The user '$admin_account' does not exist."
    }

    Write-output ''
    # Retrieve the groups that the user belongs to
    $groups = $user.MemberOf | ForEach-Object { Get-ADGroup $_ } | Sort-Object

    # Display the group names
    Write-host "User is a member of the following groups (" -ForegroundColor white -NoNewline
    Write-host "$groups_count" -ForegroundColor green -NoNewline
    Write-host "):" -ForegroundColor white
    Write-host ''  
    if (-not ($groups.Name -like "GG-ROLE*")) {
        Write-Host "- GG-ROLE* NOT assigned!!" -ForegroundColor Red
    }
    if (-not ($groups.Name -like "GG-ORG*")) {
        Write-Host "- GG-ORG* not found O: drive NOT assigned!!" -ForegroundColor Red
    }
    $groups | ForEach-Object {
        if ($_.Name -like "GG-ROLE*") {
            Write-Host "Role    : " -ForegroundColor Magenta -NoNewline
            Write-Host "$($_.Name)" -ForegroundColor Green
        } elseif ($_.Name -like "GG-ORG*") {
            Write-Host "O Drive : " -ForegroundColor Magenta -NoNewline
            Write-Host "$($_.Name)" -ForegroundColor Green
        } elseif ($_.Name -like "GG-APP*") {
            Write-Host "- $($_.Name)" -ForegroundColor yellow
#        } elseif ($_.Name -like "Group-*") {
#            Write-Host "- $($_.Name)" -ForegroundColor gray
        } else {
            Write-Output "- $($_.Name)"
        }
    }

    Write-Output ""
    Write-Output ""
    } catch {
        Write-Warning "Oops the user '$UserName' was not found!"
    }
}

Set-Alias gur Get-TCCUserRoleInfo
##>

function Get-TCCRemotePCServiceTagNumber {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [string]$ComputerName
    )
    
    $serialNumber = $null
    
    try {
        $serialNumber = Get-WmiObject -Class Win32_BIOS -ComputerName $ComputerName | Select-Object -ExpandProperty SerialNumber
    } catch {
        Write-Error "Failed to retrieve serial number from $ComputerName. Error: $($_.Exception.Message)"
    }
    
    return $serialNumber
}

function Get-CodeSigningCert {
  [CmdletBinding()]
  param (   )
  Get-ChildItem 'Cert:\CurrentUser\My\' -CodeSigningCert | Where-Object { $_.Issuer -like 'CN=Townsville City Council Issuing CA 2*' }
} function Start-ExchangeOnlineConnection {
  [CmdletBinding()]
  param (
  )   begin {
    $IsAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole("Administrators")
    if (-not (Get-Module -ListAvailable ExchangeOnlineManagement)) {
      Write-Host 'Installing ExchangeOnlineManagement module' -ForegroundColor Yellow -BackgroundColor Black
      # Magic to allow running the command below
      Get-ExecutionPolicy -List | Out-Null
      #Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned -Confirm:$false -Force       # Install module for current user only
      Install-Module ExchangeOnlineManagement -Scope CurrentUser -Force
    }     $ExoPowershellModulePath = "C:\Users\<USER>\Documents\WindowsPowerShell\Modules\ExchangeOnlineManagement\2.0.5\netFramework\Microsoft.Exchange.Management.ExoPowershellGalleryModule.dll"
    Import-Module $ExoPowershellModulePath
    $AllowBasicScriptBlock = {
      Push-Location HKLM:
      Set-ItemProperty HKLM:\Software\Policies\Microsoft\Windows\WinRM\Client -Name 'AllowBasic' -Value 1
      Pop-Location
    }
  }
  process {
    Write-Host 'Turning on Basic Authentication' -ForegroundColor Yellow -BackgroundColor Black
    # Turn on Basic Authentication via an elevated console
    if ($IsAdmin) {
      $AllowBasicScriptBlock.Invoke()
    } else {
      Start-Process powershell -Verb Runas $AllowBasicScriptBlock -Wait
    }
    # Magic to allow running the command below
    Get-ExecutionPolicy -List | Out-Null
    #Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned -Confirm:$false -Force     $CSCert = Get-CodeSigningCert 
    # Connect to Exchange Online
    if ($IsAdmin) {
      $PSSession = New-ExoPSSession -ExchangeEnvironmentName 'O365Default'
    } else {
      $UserPrincipalName = "a_$($env:USERNAME -replace '^a_')@townsville.qld.gov.au"
      $PSSession = New-ExoPSSession -ExchangeEnvironmentName 'O365Default' -UserPrincipalName $UserPrincipalName
    }
    Import-PSSession $PSSession -AllowClobber -DisableNameChecking -Certificate $CSCert
  }
  end {
  }
}


function Add-TCCComputerToFreeSpaceAssistanceGroup {
    [CmdletBinding(SupportsShouldProcess)]
    param (
        [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)]
        [string[]]$ComputerName,

        [string]$Group = 'GG-GP-Workstations-FreeSpaceAssistance'
    )

    begin {
    }

    process {

        foreach ($Computer in $ComputerName) {
            $ADComputer = Get-ADComputer -Filter "Name -like '*$Computer'"

            if ($null -ne $ADComputer) {
                if ($ADComputer.Count -gt 1) {
				    Write-Warning "'$Computer' matches more than one ADComputer object. Please select the correct one from the list."
				    $ADComputer = $ADComputer | Out-GridView -OutputMode Single
			    }
                if((Get-ADPrincipalGroupMembership -Identity $ADComputer | Where-Object { $_.Name -eq $Group } | Measure-Object).count -eq 0) {
                    Add-ADGroupMember -Identity $Group -Members $ADComputer -WhatIf:$WhatIfPreference
                    Write-Host "$Computer added. You're good to go!"
                }else{
                    Write-Warning "$Computer already exists in the Group!"
                }
            } else {
                Write-Host "WARNING: $ComputerName does not exist in AD!" -ForegroundColor Red -BackgroundColor Black
            }
        } #end of for loop
    } 

    end {
    }
}

function Remove-TCCComputerFromFreeSpaceAssistanceGroup {
    [CmdletBinding(SupportsShouldProcess)]
    param (
        [Parameter(Mandatory = $true, ValueFromPipeline = $true, ValueFromPipelineByPropertyName = $true)]
        [string[]]$ComputerName,

        [string]$Group = 'GG-GP-Workstations-FreeSpaceAssistance'
    )

    begin {
    }

    process {

        foreach ($Computer in $ComputerName) {
            $ADComputer = Get-ADComputer -Filter "Name -like '*$Computer'"

            if ($null -ne $ADComputer) {
                if ($ADComputer.Count -gt 1) {
				    Write-Warning "'$Computer' matches more than one ADComputer object. Please select the correct one from the list."
				    $ADComputer = $ADComputer | Out-GridView -OutputMode Single
			    }
                if((Get-ADPrincipalGroupMembership -Identity $ADComputer | ? { $_.Name -eq $Group } | Measure-Object).count -ne 0) {
                    Remove-ADGroupMember -Identity $Group -Members $ADComputer -WhatIf:$WhatIfPreference -Confirm:$false
                    Write-Host "$Computer Removed. You're good to go!"
                }else{
                    Write-Warning "$Computer doesn't exist in the Group!"
                }
            } else {
                Write-Host "WARNING: $ComputerName does not exist in AD!" -ForegroundColor Red -BackgroundColor Black
            }
        } #end of for loop
    } 

    end {
    }
}# function end

function Get-ADGroupMembersDetails {
    param(
        [Parameter(Mandatory = $true)]
        [string]$GroupName,
        [switch]$ExportToCSV
    )
    
    # Import the Active Directory module
    Import-Module ActiveDirectory -ErrorAction Stop

    try {
        # Get members of the AD group
        $GroupMembers = Get-ADGroupMember -Identity $GroupName -Recursive | Where-Object { $_.objectClass -eq 'user' }

        # Retrieve and return details for each member
        $MembersDetails = $GroupMembers | ForEach-Object {
            $UserDetails = Get-ADUser -Identity $_.distinguishedName -Properties SamAccountName, Name, Description, Enabled
            [PSCustomObject]@{
                UserID      = $UserDetails.SamAccountName
                Username    = $UserDetails.Name
                Description = $UserDetails.Description
                Active      = $UserDetails.Enabled
            }
        }

        # If ExportToCSV is specified, save results to a CSV file
        if ($ExportToCSV) {
            $FilePath = "D:\$GroupName.csv"
            $MembersDetails | Export-Csv -Path $FilePath -NoTypeInformation -Encoding UTF8
            Write-Output "CSV file generated at: $FilePath"
        }

        # Return the details
        return $MembersDetails
    } catch {
        Write-Error "An error occurred: $_"
        return $null
    }
}

function Get-ADGroupMembersDetails {
    param(
        [Parameter(Mandatory = $true)]
        [string]$GroupName,
        [switch]$ExportToCSV,
        [string]$SortBy,
        [string[]]$UsersToSearch
    )
    
    # Import the Active Directory module
    Import-Module ActiveDirectory -ErrorAction Stop

    try {
        # Get members of the AD group, including users, computers, and groups
        $GroupMembers = Get-ADGroupMember -Identity $GroupName -Recursive | Where-Object { $_.objectClass -in @('user', 'computer', 'group') }

        # Retrieve and store details for each member
        $MembersDetails = $GroupMembers | ForEach-Object {
            if ($_.objectClass -eq 'user') {
                $UserDetails = Get-ADUser -Identity $_.distinguishedName -Properties SamAccountName, Name, Description, Enabled
                [PSCustomObject]@{
                    ObjectType  = "User"
                    ID          = $UserDetails.SamAccountName
                    Name        = $UserDetails.Name
                    Description = $UserDetails.Description
                    Active      = $UserDetails.Enabled
                }
            } elseif ($_.objectClass -eq 'computer') {
                $ComputerDetails = Get-ADComputer -Identity $_.distinguishedName -Properties SamAccountName, Name, Description, Enabled
                [PSCustomObject]@{
                    ObjectType  = "Computer"
                    ID          = $ComputerDetails.SamAccountName
                    Name        = $ComputerDetails.Name
                    Description = $ComputerDetails.Description
                    Active      = $ComputerDetails.Enabled
                }
            } elseif ($_.objectClass -eq 'group') {
                $GroupDetails = Get-ADGroup -Identity $_.distinguishedName -Properties SamAccountName, Name, Description
                [PSCustomObject]@{
                    ObjectType  = "Group"
                    ID          = $GroupDetails.SamAccountName
                    Name        = $GroupDetails.Name
                    Description = $GroupDetails.Description
                    Active      = $null # Groups don't have an enabled/disabled state
                }
            }
        }

        # Check for specific users, computers, or groups if provided
        if ($UsersToSearch) {
            $UsersToSearch = $UsersToSearch -split ',' | ForEach-Object { $_.Trim() } # Split and trim input
            $SearchResults = foreach ($Item in $UsersToSearch) {
                $Match = $MembersDetails | Where-Object { $_.ID -eq $Item -or $_.Name -eq $Item }
                if ($Match) {
                    # Match found - return the same layout as $MembersDetails
                    $Match
                } else {
                    # No match - return a placeholder in the same layout
                    [PSCustomObject]@{
                        ObjectType  = "N/A"
                        ID          = $null
                        Name        = $Item
                        Description = "Not found in the group"
                        Active      = $null
                    }
                }
            }

            # Output the search results in table format
            return $SearchResults | Format-Table -AutoSize
        }

        # Sort the results by the specified column, if provided
        if ($SortBy) {
            $MembersDetails = $MembersDetails | Sort-Object -Property $SortBy
        }

        # If ExportToCSV is specified, save results to a CSV file
        if ($ExportToCSV) {
            $FilePath = "D:\$GroupName.csv"
            $MembersDetails | Export-Csv -Path $FilePath -NoTypeInformation -Encoding UTF8
            Write-Output "CSV file generated at: $FilePath"
        }

        # Return the member details in table format
        return $MembersDetails | Format-Table -AutoSize
    } catch {
        Write-Error "An error occurred: $_"
        return $null
    }
}

#Get-ADGroupMembersDetails -GroupName "GG-APP-OKTA-MyGeotab" -UsersToSearch "Rick jara"
#Get-ADGroupMembersDetails -GroupName "GG-APP-OKTA-MyGeotab" -UsersToSearch rxj

Set-Alias ggm Get-ADGroupMembersDetails
#ggm -GroupName "GG-APP-OKTA-MyGeotab" -UsersToSearch rxj

#Set-Alias gug Get-ADGroupMembersDetails -GroupName $GroupName -ExportToCSV | Format-Table -AutoSize

Function Get-TCCCompterGroupMembership {
    param(
        $ComputerName
    )
    Get-ADPrincipalGroupMembership (Get-ADComputer $ComputerName) | select-object name
}

Set-Alias gpcg Get-TCCCompterGroupMembership

function Get-TCCPP{
    $TCC_profile_path = '\Documents\WindowsPowerShell\profile.ps1' 

    if ($env:COMPUTERNAME -eq 'ADMIN01'){
        $home_drv = 'C:'
    }else{
        $home_drv = 'D:'
    }

    return $home_drv + '\Users\' + $env:USERNAME + $TCC_profile_path   
}  

Get-TCCPP

Function Get-MyCommands {

    $TCC_profile = Get-TCCPP   
    $linel = '----------------------------------------------------------------------'
    $lines = '-----------------------------------------------------'
    Write-Host $lines
    Write-Host My Custom Functions:
    Write-Host $lines
    Get-Content -Path $TCC_profile | Select-String -Pattern "^function.+" | ForEach-Object {
        # Find function names that contains letters, numbers and dashes
        [Regex]::Matches($_, "^function ([a-z0-9.-]+)","IgnoreCase").Groups[1].Value
    } | Where-Object { $_ -ine "prompt" } #| Sort-Object
    write-host $lines
    write-host "My Aliases:"
    write-host $lines

    Select-String -Path $TCC_profile -Pattern "Set-Alias" | 
        ForEach-Object { 
           $item = $_.ToString().Split(' ') | Select-Object -Skip 1 
           write-host $item[0] -ForegroundColor green -NoNewline
           write-host " " -NoNewline
           write-host $item[1] -ForegroundColor white
            }

    Write-Host $linel
    Write-Host 'in' $TCC_profile
    Write-Host $linel
}

function Get-TCCPCFreeSpace {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [string[]]$ComputerName
    )
    foreach ($computer in $ComputerName) {
        Write-Host "Computer Name: $($computer.ToUpper())" -ForegroundColor Yellow
        if (Test-Connection -ComputerName $computer -Count 1 -Quiet) {
            Get-WmiObject -Class Win32_LogicalDisk -ComputerName $computer -Filter "DriveType=3" |
                Select-Object DeviceID, MediaType, VolumeName,
                    @{Name="Size(GB)";Expression={[math]::Round($_.Size/1GB,2)}},
                    @{Name="FreeSpace(GB)";Expression={[math]::Round($_.FreeSpace/1GB,2)}},
                    @{Name="FreeSpace(%)";Expression={[math]::Round(($_.FreeSpace/$_.Size)*100,2)}} | 
                        Format-Table -AutoSize | Out-String -Width 1024 | ForEach-Object { $_ -replace "`n", "`n`t" } | Write-Host -ForegroundColor Green
        }
        else {
            Write-Host "Unable to reach $computer" -ForegroundColor Red
        }
    }
}

# get-TCCPCFreeSpace $env:COMPUTERNAME
 


#get-aduser -Filter * | Sort-Object -Property SamAccountName | where { $_.samaccountname -match '[c][a-z0-9][h]'} | ft SamAccountName, Name


#Get-ADGroupMember -identity "GG-APP-MECM-Optus APN - COMPUTERS" | Select-Object Name | Sort-Object -Property Name

#c:\Users\<USER>\Documents\WindowsPowerShell\profile.ps1



function Invoke-CodeSigning {
    param (
        [Parameter(Mandatory=$true)]
        [string]$FileOrFolderPath
    )

    $CSCert = Get-ChildItem 'Cert:\CurrentUser\My\' -CodeSigningCert | Where-Object { $_.Issuer -like 'CN=Townsville City Council Issuing CA 2*' }
    $TimestampServer = 'http://timestamp.digicert.com'

    Get-ChildItem -LiteralPath $FileOrFolderPath -Recurse -Filter '*.ps*1' | Where-Object { $_.Extension -eq '.ps1' -or $_.Extension -eq '.psm1' } | ForEach-Object {
        Set-AuthenticodeSignature -Certificate $CSCert -TimestampServer $TimestampServer -FilePath $_.FullName
    }
}

function Get-ADGroupMemberList {
# Usage Example: Get-ADGroupMemberList "GG-SEC-LDCC Computers"
    param (
        [Parameter(Mandatory=$true)]
        [string]$GroupName,
        [string]$OutputFile = "D:\\$GroupName.csv"
    )
    Get-ADGroupMember -Identity $GroupName | Select-Object Name | 
    Export-Csv -Path "D:\\$GroupName-$((Get-Date).ToString('yyyy-MM-dd')).csv" -NoTypeInformation -Force
}

set-alias ggml Get-ADGroupMemberList 

function Get_LDCCCoputersInADCollection{
    $ADGroup = "GG-SEC-LDCC Computers"
    Get-ADGroupMemberList $ADGroup
}

set-alias gldccpc Get_LDCCCoputersInADCollection



function Sig-TCCProfile{
    $TCC_profile = Get-TCCPP 
    Invoke-CodeSigning $TCC_profile
}
set-alias *ss Sig-TCCProfile
Set-Alias myc Get-MyCommands
Set-alias gpcf Get-TCCPCFreeSpace
#Get-MyCommands

# SIG # Begin signature block
# MIIl7wYJKoZIhvcNAQcCoIIl4DCCJdwCAQExCzAJBgUrDgMCGgUAMGkGCisGAQQB
# gjcCAQSgWzBZMDQGCisGAQQBgjcCAR4wJgIDAQAABBAfzDtgWUsITrck0sYpfvNR
# AgEAAgEAAgEAAgEAAgEAMCEwCQYFKw4DAhoFAAQUEnjtcGBsunA2C/f0VtWPtpk3
# 8Cyggh/XMIIFjTCCBHWgAwIBAgIQDpsYjvnQLefv21DiCEAYWjANBgkqhkiG9w0B
# AQwFADBlMQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYD
# VQQLExB3d3cuZGlnaWNlcnQuY29tMSQwIgYDVQQDExtEaWdpQ2VydCBBc3N1cmVk
# IElEIFJvb3QgQ0EwHhcNMjIwODAxMDAwMDAwWhcNMzExMTA5MjM1OTU5WjBiMQsw
# CQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3d3cu
# ZGlnaWNlcnQuY29tMSEwHwYDVQQDExhEaWdpQ2VydCBUcnVzdGVkIFJvb3QgRzQw
# ggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQC/5pBzaN675F1KPDAiMGkz
# 7MKnJS7JIT3yithZwuEppz1Yq3aaza57G4QNxDAf8xukOBbrVsaXbR2rsnnyyhHS
# 5F/WBTxSD1Ifxp4VpX6+n6lXFllVcq9ok3DCsrp1mWpzMpTREEQQLt+C8weE5nQ7
# bXHiLQwb7iDVySAdYyktzuxeTsiT+CFhmzTrBcZe7FsavOvJz82sNEBfsXpm7nfI
# SKhmV1efVFiODCu3T6cw2Vbuyntd463JT17lNecxy9qTXtyOj4DatpGYQJB5w3jH
# trHEtWoYOAMQjdjUN6QuBX2I9YI+EJFwq1WCQTLX2wRzKm6RAXwhTNS8rhsDdV14
# Ztk6MUSaM0C/CNdaSaTC5qmgZ92kJ7yhTzm1EVgX9yRcRo9k98FpiHaYdj1ZXUJ2
# h4mXaXpI8OCiEhtmmnTK3kse5w5jrubU75KSOp493ADkRSWJtppEGSt+wJS00mFt
# 6zPZxd9LBADMfRyVw4/3IbKyEbe7f/LVjHAsQWCqsWMYRJUadmJ+9oCw++hkpjPR
# iQfhvbfmQ6QYuKZ3AeEPlAwhHbJUKSWJbOUOUlFHdL4mrLZBdd56rF+NP8m800ER
# ElvlEFDrMcXKchYiCd98THU/Y+whX8QgUWtvsauGi0/C1kVfnSD8oR7FwI+isX4K
# Jpn15GkvmB0t9dmpsh3lGwIDAQABo4IBOjCCATYwDwYDVR0TAQH/BAUwAwEB/zAd
# BgNVHQ4EFgQU7NfjgtJxXWRM3y5nP+e6mK4cD08wHwYDVR0jBBgwFoAUReuir/SS
# y4IxLVGLp6chnfNtyA8wDgYDVR0PAQH/BAQDAgGGMHkGCCsGAQUFBwEBBG0wazAk
# BggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEMGCCsGAQUFBzAC
# hjdodHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURS
# b290Q0EuY3J0MEUGA1UdHwQ+MDwwOqA4oDaGNGh0dHA6Ly9jcmwzLmRpZ2ljZXJ0
# LmNvbS9EaWdpQ2VydEFzc3VyZWRJRFJvb3RDQS5jcmwwEQYDVR0gBAowCDAGBgRV
# HSAAMA0GCSqGSIb3DQEBDAUAA4IBAQBwoL9DXFXnOF+go3QbPbYW1/e/Vwe9mqyh
# hyzshV6pGrsi+IcaaVQi7aSId229GhT0E0p6Ly23OO/0/4C5+KH38nLeJLxSA8hO
# 0Cre+i1Wz/n096wwepqLsl7Uz9FDRJtDIeuWcqFItJnLnU+nBgMTdydE1Od/6Fmo
# 8L8vC6bp8jQ87PcDx4eo0kxAGTVGamlUsLihVo7spNU96LHc/RzY9HdaXFSMb++h
# UD38dglohJ9vytsgjTVgHAIDyyCwrFigDkBjxZgiwbJZ9VVrzyerbHbObyMt9H5x
# aiNrIv8SuFQtJ37YOtnwtoeW/VvRXKwYw02fc7cBqZ9Xql4o4rmUMIIGWDCCBUCg
# AwIBAgITIgAAOcIRmtcEwivynwAAAAA5wjANBgkqhkiG9w0BAQsFADCBpDESMBAG
# CgmSJomT8ixkARkWAmF1MRMwEQYKCZImiZPyLGQBGRYDZ292MRMwEQYKCZImiZPy
# LGQBGRYDcWxkMRowGAYKCZImiZPyLGQBGRYKdG93bnN2aWxsZTEZMBcGCgmSJomT
# 8ixkARkWCWNvcnBvcmF0ZTEtMCsGA1UEAxMkVG93bnN2aWxsZSBDaXR5IENvdW5j
# aWwgSXNzdWluZyBDQSAyMB4XDTIzMTIxMzIzMzcxNFoXDTI3MDYyODIzNDUxN1ow
# gcQxEjAQBgoJkiaJk/IsZAEZFgJhdTETMBEGCgmSJomT8ixkARkWA2dvdjETMBEG
# CgmSJomT8ixkARkWA3FsZDEaMBgGCgmSJomT8ixkARkWCnRvd25zdmlsbGUxGTAX
# BgoJkiaJk/IsZAEZFgljb3Jwb3JhdGUxFzAVBgNVBAsTDkFkbWluaXN0cmF0b3Jz
# MRowGAYDVQQLExFTeW5jIHdpdGggQXp1cmVBRDEYMBYGA1UEAxMPUmljayBKYXJh
# IEFkbWluMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp8eziU0Cp9L9
# mt+FIJxi4Fvy5Ds0xZ6ZERRuU225aOc9KBAT4aqZBphDXgiGieQCXnHBFFnZsRW7
# Ti2v+XhA1mpLdJc6iRAgQUmtBV02vXDj3IPPlt22N8DxI8DFNIxnEmxUTTinB5uQ
# h7btj53TqYK0zaAgPselfm+eWkzJ1YxHoqvLNqCVFaOgDgRXF1vpMyofguuQYfum
# GJp1SlyhmTRje2wIFAr1hUTHPkuoUW8RG2JuTDmVDZvwWW0MRcCvvRyVTFpb9d4G
# BxaJnLr4LKJkCWRnOjHaMLxs+qs34nWS9rUEKgG2mOegAOSm0XybharePGUKkobD
# gAYTvGsfnQIDAQABo4ICXzCCAlswPQYJKwYBBAGCNxUHBDAwLgYmKwYBBAGCNxUI
# gaKRG4fTq2WBnYskhZOUHoaR+EyBF4OZNYXP8lECAWQCAQcwEwYDVR0lBAwwCgYI
# KwYBBQUHAwMwCwYDVR0PBAQDAgeAMBsGCSsGAQQBgjcVCgQOMAwwCgYIKwYBBQUH
# AwMwHQYDVR0OBBYEFJK6Fyc74JghDi0xDmszFst4P5SkMB8GA1UdIwQYMBaAFPx2
# jcbLHYE51+pzYVjHn7JBzIOlMGgGA1UdHwRhMF8wXaBboFmGV2h0dHA6Ly9jcmwu
# dG93bnN2aWxsZS5xbGQuZ292LmF1L2NybC9Ub3duc3ZpbGxlJTIwQ2l0eSUyMENv
# dW5jaWwlMjBJc3N1aW5nJTIwQ0ElMjAyLmNybDCBqAYIKwYBBQUHAQEEgZswgZgw
# YwYIKwYBBQUHMAKGV2h0dHA6Ly9wa2kudG93bnN2aWxsZS5xbGQuZ292LmF1L3Br
# aS9Ub3duc3ZpbGxlJTIwQ2l0eSUyMENvdW5jaWwlMjBJc3N1aW5nJTIwQ0ElMjAy
# LmNydDAxBggrBgEFBQcwAYYlaHR0cDovL3BraS50b3duc3ZpbGxlLnFsZC5nb3Yu
# YXUvb2NzcDA2BgNVHREELzAtoCsGCisGAQQBgjcUAgOgHQwbYV9yeGpAdG93bnN2
# aWxsZS5xbGQuZ292LmF1ME4GCSsGAQQBgjcZAgRBMD+gPQYKKwYBBAGCNxkCAaAv
# BC1TLTEtNS0yMS03MjUzNDU1NDMtMTI5MjQyODA5My04Mzk1MjIxMTUtNjkxNDcw
# DQYJKoZIhvcNAQELBQADggEBAD+frk/I/cc8DZEh4YPPeKmMkvUuUvIaUfc5/zXP
# fuZ8l1dTWy+FBZouOaBqp0KHwKnsEMX6PhRhHRjefqSTxWupo7XIKUwcC30uGcj1
# s9ER6QaZU1/u+GeJuebQtYsUlD6DShXXzxbOPEFFdt24xdHby+/KeM7q9Pjncme0
# MOxCkkqTnjnIswsK3BMb4u25K74tOuP+kE7Iy19GCiNpHZtSdhGd/QVdMNl5RInj
# J2NKcvAWOVm9/JBeygxutQLj2jxlj8c1W3Wb2p4ZpcvDLqi+Tu1mMO2gpmt5TaPn
# M3jsG1Ya3twGbp4Ts2ZPaBp7pc4i3cn14six8qHJ2/W06lYwggZ0MIIEXKADAgEC
# AhM4AAAABMudnipP6sz8AAAAAAAEMA0GCSqGSIb3DQEBDAUAMCwxKjAoBgNVBAMT
# IVRvd25zdmlsbGUgQ2l0eSBDb3VuY2lsIFJvb3QgQ0EgMjAeFw0xNzA2MjgyMzM1
# MTdaFw0yNzA2MjgyMzQ1MTdaMIGkMRIwEAYKCZImiZPyLGQBGRYCYXUxEzARBgoJ
# kiaJk/IsZAEZFgNnb3YxEzARBgoJkiaJk/IsZAEZFgNxbGQxGjAYBgoJkiaJk/Is
# ZAEZFgp0b3duc3ZpbGxlMRkwFwYKCZImiZPyLGQBGRYJY29ycG9yYXRlMS0wKwYD
# VQQDEyRUb3duc3ZpbGxlIENpdHkgQ291bmNpbCBJc3N1aW5nIENBIDIwggEiMA0G
# CSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDBD1SOqGpX1jqJoplNmGcZYzSemVXy
# knPG5u6vOXWKI/Q2TdtmtS10lTBu3RMm9/ZnYOuj95SXALdnoYPaO77nWFebPezm
# kutiUCe1FP9jm+16cMxMegR3SfQ5rv+bBnWAV9+H6qBHYjYJdxNR/NF6U/7c0udp
# /Ng8kyjKHLmUMBTVN4Z1cACInQUhE0MDlT/E8qI1/jER+pMtSZ7vNMDhpqx7zS1+
# qLkvcLTmOV8Cw5D9UI+wH/sok4qrrrYq/doHc9JHRQL7X0fwBEMR75+M6LWtDNuc
# inBvkQVNjG6+ssP6xRiV4UfWcE3VYyFyZgAo5LCU17RaWIx7VqQcLrBFAgMBAAGj
# ggIUMIICEDAQBgkrBgEEAYI3FQEEAwIBADAdBgNVHQ4EFgQU/HaNxssdgTnX6nNh
# WMefskHMg6UwgakGA1UdIASBoTCBnjCBmwYgKoZIhvcUAb5Ak3qBnwSabIPeNIGA
# CoLJHd7LJoPdwDIwdzA6BggrBgEFBQcCAjAuHiwATABlAGcAYQBsACAAUABvAGwA
# aQBjAHkAIABTAHQAYQB0AGUAbQBlAG4AdDA5BggrBgEFBQcCARYtaHR0cDovL3Br
# aS50b3duc3ZpbGxlLnFsZC5nb3YuYXUvcGtpL2Nwcy5odG1sMBkGCSsGAQQBgjcU
# AgQMHgoAUwB1AGIAQwBBMAsGA1UdDwQEAwIBhjAPBgNVHRMBAf8EBTADAQH/MB8G
# A1UdIwQYMBaAFO9x0VwY89jp2/M88EnwZCD23aTtMGUGA1UdHwReMFwwWqBYoFaG
# VGh0dHA6Ly9jcmwudG93bnN2aWxsZS5xbGQuZ292LmF1L2NybC9Ub3duc3ZpbGxl
# JTIwQ2l0eSUyMENvdW5jaWwlMjBSb290JTIwQ0ElMjAyLmNybDBwBggrBgEFBQcB
# AQRkMGIwYAYIKwYBBQUHMAKGVGh0dHA6Ly9wa2kudG93bnN2aWxsZS5xbGQuZ292
# LmF1L3BraS9Ub3duc3ZpbGxlJTIwQ2l0eSUyMENvdW5jaWwlMjBSb290JTIwQ0El
# MjAyLmNydDANBgkqhkiG9w0BAQwFAAOCAgEAtzanHHZYQr0BaJOnZ9oQQ9PSsGwB
# 62pVBRWdi6ijdlnQ9Jb/3BP3v6NXD7RE+InI4qX/gZq0Z+HPAFFI4j7b/XmfEjW7
# +scyPfhgtyKLk/x8y3yAwBWUmygve3Zrp0ALwVKt/7KUvc8B3k/AnDGWuQXBErkU
# VXxlCGxupI5Cr/cUs3RWkKFaNWyJjMv1x19C7iJKy4eUmKJeDAlaTje5IPfaaDTr
# TDZNpAEzBMVdXo4c15xxy0DoE6+JBc6VRwCnufEGqi7p/OhNyEDdIlwohYqKbKW+
# YWpyYiW/5mLgCHJZsoua90hD8UWmI7oY/fdue4n/dnEa3NDpunkLW9cf0cBTEGAx
# /oscp5Mvg/m+J1VDz193yT18x8S8qOIyVZi7LTP3+gL1NVHfvqxF012u6VRwhtVX
# uAGSkH8AvcovFiU1kkVKkgzj9j09YamsZWMOehrep7Q+glZfTcBjCKhK4FY/m4qV
# FnDrf8ltyrWGGRQG7wM8Of6qXJLcQZaEswuTafLQQHtigRfya/qh3d2hfSjxJ106
# 3E7Ubk4DZ8OCpmRwCKz60oIO06sneWdPrc6GftKwaHGG7DJmySX5ggVtUiZrF5KY
# LWOwwew6qLQGPtKXndJ1ctrlnjB7YTYikeyRf+d+IgE7t392Br344Xd3g719AvEc
# prMJPx4+hHNmR38wggauMIIElqADAgECAhAHNje3JFR82Ees/ShmKl5bMA0GCSqG
# SIb3DQEBCwUAMGIxCzAJBgNVBAYTAlVTMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMx
# GTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xITAfBgNVBAMTGERpZ2lDZXJ0IFRy
# dXN0ZWQgUm9vdCBHNDAeFw0yMjAzMjMwMDAwMDBaFw0zNzAzMjIyMzU5NTlaMGMx
# CzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5EaWdpQ2VydCwgSW5jLjE7MDkGA1UEAxMy
# RGlnaUNlcnQgVHJ1c3RlZCBHNCBSU0E0MDk2IFNIQTI1NiBUaW1lU3RhbXBpbmcg
# Q0EwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDGhjUGSbPBPXJJUVXH
# JQPE8pE3qZdRodbSg9GeTKJtoLDMg/la9hGhRBVCX6SI82j6ffOciQt/nR+eDzMf
# UBMLJnOWbfhXqAJ9/UO0hNoR8XOxs+4rgISKIhjf69o9xBd/qxkrPkLcZ47qUT3w
# 1lbU5ygt69OxtXXnHwZljZQp09nsad/ZkIdGAHvbREGJ3HxqV3rwN3mfXazL6IRk
# tFLydkf3YYMZ3V+0VAshaG43IbtArF+y3kp9zvU5EmfvDqVjbOSmxR3NNg1c1eYb
# qMFkdECnwHLFuk4fsbVYTXn+149zk6wsOeKlSNbwsDETqVcplicu9Yemj052FVUm
# cJgmf6AaRyBD40NjgHt1biclkJg6OBGz9vae5jtb7IHeIhTZgirHkr+g3uM+onP6
# 5x9abJTyUpURK1h0QCirc0PO30qhHGs4xSnzyqqWc0Jon7ZGs506o9UD4L/wojzK
# QtwYSH8UNM/STKvvmz3+DrhkKvp1KCRB7UK/BZxmSVJQ9FHzNklNiyDSLFc1eSuo
# 80VgvCONWPfcYd6T/jnA+bIwpUzX6ZhKWD7TA4j+s4/TXkt2ElGTyYwMO1uKIqjB
# Jgj5FBASA31fI7tk42PgpuE+9sJ0sj8eCXbsq11GdeJgo1gJASgADoRU7s7pXche
# MBK9Rp6103a50g5rmQzSM7TNsQIDAQABo4IBXTCCAVkwEgYDVR0TAQH/BAgwBgEB
# /wIBADAdBgNVHQ4EFgQUuhbZbU2FL3MpdpovdYxqII+eyG8wHwYDVR0jBBgwFoAU
# 7NfjgtJxXWRM3y5nP+e6mK4cD08wDgYDVR0PAQH/BAQDAgGGMBMGA1UdJQQMMAoG
# CCsGAQUFBwMIMHcGCCsGAQUFBwEBBGswaTAkBggrBgEFBQcwAYYYaHR0cDovL29j
# c3AuZGlnaWNlcnQuY29tMEEGCCsGAQUFBzAChjVodHRwOi8vY2FjZXJ0cy5kaWdp
# Y2VydC5jb20vRGlnaUNlcnRUcnVzdGVkUm9vdEc0LmNydDBDBgNVHR8EPDA6MDig
# NqA0hjJodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRUcnVzdGVkUm9v
# dEc0LmNybDAgBgNVHSAEGTAXMAgGBmeBDAEEAjALBglghkgBhv1sBwEwDQYJKoZI
# hvcNAQELBQADggIBAH1ZjsCTtm+YqUQiAX5m1tghQuGwGC4QTRPPMFPOvxj7x1Bd
# 4ksp+3CKDaopafxpwc8dB+k+YMjYC+VcW9dth/qEICU0MWfNthKWb8RQTGIdDAiC
# qBa9qVbPFXONASIlzpVpP0d3+3J0FNf/q0+KLHqrhc1DX+1gtqpPkWaeLJ7giqzl
# /Yy8ZCaHbJK9nXzQcAp876i8dU+6WvepELJd6f8oVInw1YpxdmXazPByoyP6wCeC
# RK6ZJxurJB4mwbfeKuv2nrF5mYGjVoarCkXJ38SNoOeY+/umnXKvxMfBwWpx2cYT
# gAnEtp/Nh4cku0+jSbl3ZpHxcpzpSwJSpzd+k1OsOx0ISQ+UzTl63f8lY5knLD0/
# a6fxZsNBzU+2QJshIUDQtxMkzdwdeDrknq3lNHGS1yZr5Dhzq6YBT70/O3itTK37
# xJV77QpfMzmHQXh6OOmc4d0j/R0o08f56PGYX/sr2H7yRp11LB4nLCbbbxV7HhmL
# NriT1ObyF5lZynDwN7+YAN8gFk8n+2BnFqFmut1VwDophrCYoCvtlUG3OtUVmDG0
# YgkPCr2B2RP+v6TR81fZvAT6gt4y3wSJ8ADNXcL50CN/AAvkdgIm2fBldkKmKYcJ
# RyvmfxqkhQ/8mJb2VVQrH4D6wPIOK+XW+6kvRBVK5xMOHds3OBqhK/bt1nz8MIIG
# vDCCBKSgAwIBAgIQC65mvFq6f5WHxvnpBOMzBDANBgkqhkiG9w0BAQsFADBjMQsw
# CQYDVQQGEwJVUzEXMBUGA1UEChMORGlnaUNlcnQsIEluYy4xOzA5BgNVBAMTMkRp
# Z2lDZXJ0IFRydXN0ZWQgRzQgUlNBNDA5NiBTSEEyNTYgVGltZVN0YW1waW5nIENB
# MB4XDTI0MDkyNjAwMDAwMFoXDTM1MTEyNTIzNTk1OVowQjELMAkGA1UEBhMCVVMx
# ETAPBgNVBAoTCERpZ2lDZXJ0MSAwHgYDVQQDExdEaWdpQ2VydCBUaW1lc3RhbXAg
# MjAyNDCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAL5qc5/2lSGrljC6
# W23mWaO16P2RHxjEiDtqmeOlwf0KMCBDEr4IxHRGd7+L660x5XltSVhhK64zi9Ce
# C9B6lUdXM0s71EOcRe8+CEJp+3R2O8oo76EO7o5tLuslxdr9Qq82aKcpA9O//X6Q
# E+AcaU/byaCagLD/GLoUb35SfWHh43rOH3bpLEx7pZ7avVnpUVmPvkxT8c2a2yC0
# WMp8hMu60tZR0ChaV76Nhnj37DEYTX9ReNZ8hIOYe4jl7/r419CvEYVIrH6sN00y
# x49boUuumF9i2T8UuKGn9966fR5X6kgXj3o5WHhHVO+NBikDO0mlUh902wS/Eeh8
# F/UFaRp1z5SnROHwSJ+QQRZ1fisD8UTVDSupWJNstVkiqLq+ISTdEjJKGjVfIcsg
# A4l9cbk8Smlzddh4EfvFrpVNnes4c16Jidj5XiPVdsn5n10jxmGpxoMc6iPkoaDh
# i6JjHd5ibfdp5uzIXp4P0wXkgNs+CO/CacBqU0R4k+8h6gYldp4FCMgrXdKWfM4N
# 0u25OEAuEa3JyidxW48jwBqIJqImd93NRxvd1aepSeNeREXAu2xUDEW8aqzFQDYm
# r9ZONuc2MhTMizchNULpUEoA6Vva7b1XCB+1rxvbKmLqfY/M/SdV6mwWTyeVy5Z/
# JkvMFpnQy5wR14GJcv6dQ4aEKOX5AgMBAAGjggGLMIIBhzAOBgNVHQ8BAf8EBAMC
# B4AwDAYDVR0TAQH/BAIwADAWBgNVHSUBAf8EDDAKBggrBgEFBQcDCDAgBgNVHSAE
# GTAXMAgGBmeBDAEEAjALBglghkgBhv1sBwEwHwYDVR0jBBgwFoAUuhbZbU2FL3Mp
# dpovdYxqII+eyG8wHQYDVR0OBBYEFJ9XLAN3DigVkGalY17uT5IfdqBbMFoGA1Ud
# HwRTMFEwT6BNoEuGSWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2VydFRy
# dXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcmwwgZAGCCsGAQUF
# BwEBBIGDMIGAMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5kaWdpY2VydC5jb20w
# WAYIKwYBBQUHMAKGTGh0dHA6Ly9jYWNlcnRzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2Vy
# dFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcnQwDQYJKoZI
# hvcNAQELBQADggIBAD2tHh92mVvjOIQSR9lDkfYR25tOCB3RKE/P09x7gUsmXqt4
# 0ouRl3lj+8QioVYq3igpwrPvBmZdrlWBb0HvqT00nFSXgmUrDKNSQqGTdpjHsPy+
# LaalTW0qVjvUBhcHzBMutB6HzeledbDCzFzUy34VarPnvIWrqVogK0qM8gJhh/+q
# DEAIdO/KkYesLyTVOoJ4eTq7gj9UFAL1UruJKlTnCVaM2UeUUW/8z3fvjxhN6hdT
# 98Vr2FYlCS7Mbb4Hv5swO+aAXxWUm3WpByXtgVQxiBlTVYzqfLDbe9PpBKDBfk+r
# abTFDZXoUke7zPgtd7/fvWTlCs30VAGEsshJmLbJ6ZbQ/xll/HjO9JbNVekBv2Tg
# em+mLptR7yIrpaidRJXrI+UzB6vAlk/8a1u7cIqV0yef4uaZFORNekUgQHTqddms
# PCEIYQP7xGxZBIhdmm4bhYsVA6G2WgNFYagLDBzpmk9104WQzYuVNsxyoVLObhx3
# RugaEGru+SojW4dHPoWrUhftNpFC5H7QEY7MhKRyrBe7ucykW7eaCuWBsBb4HOKR
# FVDcrZgdwaSIqMDiCLg4D+TPVgKx2EgEdeoHNHT9l3ZDBD+XgbF+23/zBjeCtxz+
# dL/9NWR6P2eZRi7zcEO1xwcdcqJsyz/JceENc2Sg8h3KeFUCS7tpFk7CrDqkMYIF
# gjCCBX4CAQEwgbwwgaQxEjAQBgoJkiaJk/IsZAEZFgJhdTETMBEGCgmSJomT8ixk
# ARkWA2dvdjETMBEGCgmSJomT8ixkARkWA3FsZDEaMBgGCgmSJomT8ixkARkWCnRv
# d25zdmlsbGUxGTAXBgoJkiaJk/IsZAEZFgljb3Jwb3JhdGUxLTArBgNVBAMTJFRv
# d25zdmlsbGUgQ2l0eSBDb3VuY2lsIElzc3VpbmcgQ0EgMgITIgAAOcIRmtcEwivy
# nwAAAAA5wjAJBgUrDgMCGgUAoHgwGAYKKwYBBAGCNwIBDDEKMAigAoAAoQKAADAZ
# BgkqhkiG9w0BCQMxDAYKKwYBBAGCNwIBBDAcBgorBgEEAYI3AgELMQ4wDAYKKwYB
# BAGCNwIBFTAjBgkqhkiG9w0BCQQxFgQUkdCTqx052zv9G85lKFVUnMyxsoIwDQYJ
# KoZIhvcNAQEBBQAEggEAZFLW2ak1GnCp9vRATBqWhogGbZcdu8RGW97gKwLM9+My
# gjBmAESL4U5aufrbDIwuAXpDu3/aJNEkk9SoNQORLcchezCGAIC/jX2OwBN01pzl
# joQh6v+ctGSrvns7w95rZFZfIQSUHgrRpoL3HydOM99WX/8Y2rzvLhDQBw/CQBNs
# BzSOYrBEkfLX6zwouOh5P0gfMdNzbfi0tsZvFyRN1EjyhgPi+AlYtF5CrupqfBIM
# FgO6sWYIEtYwvZ5HdNyC5Fk3rsOW8It8b/2bu0IIf+ltlQeCDcVFTSK7zH/RvA7l
# JtzyBvwzIcv4KKj51pHlJwhbCKtd7N/xmCcKo0OumaGCAyAwggMcBgkqhkiG9w0B
# CQYxggMNMIIDCQIBATB3MGMxCzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5EaWdpQ2Vy
# dCwgSW5jLjE7MDkGA1UEAxMyRGlnaUNlcnQgVHJ1c3RlZCBHNCBSU0E0MDk2IFNI
# QTI1NiBUaW1lU3RhbXBpbmcgQ0ECEAuuZrxaun+Vh8b56QTjMwQwDQYJYIZIAWUD
# BAIBBQCgaTAYBgkqhkiG9w0BCQMxCwYJKoZIhvcNAQcBMBwGCSqGSIb3DQEJBTEP
# Fw0yNTA0MDkwMTMyMTdaMC8GCSqGSIb3DQEJBDEiBCBOM/m3ETpJ0VM0Js+mLpuN
# mnI5G/hJ8ymZ6pfsKJSGsTANBgkqhkiG9w0BAQEFAASCAgBj0bEu1/dbJorQb+Qh
# upsrPUu+khzhC0mUc2j53PfGpu9zjc337yEw2SF96hv0OxIJbURhsV7Mf9aF2Dxl
# deD4jie1SWiMfSaiVzYEm5lUCIB7IOx/XtUZP1EzZCKB4/wjzvLQHKFoIdRBt50P
# MJR+EwoXav3gLBXSsDB6UXjFAJt2ssJSYqtqIyjUtMR3qEucieKvs22d93UuyaPM
# XZdgLt7fmGeR+nb/XW0mpCC4MRT2wmidRPkUCXvKnSn47bN5T3bwRbbgzDkfSSXO
# Jq6/P996Yet4GGyORAwrXq5HHKwMt9783JLYYtLdxISrKpH7RJXMfF3vPl27caOn
# FkMoHzEprOU0sikyMT+h5skq7pVQauL7O3h2jaMDGLw2C+jSNL2vuH3doqBJl1uc
# nT+YvKhdRSc/G9XsE4QS1WKIxek2usvlojBmFCvprTOMcif6xzKUKY+JuekOL2jc
# T/415WOOCHDiFPQ41B8bc19gszh2MvNJwDV1jQKxQW1jiy9uv/SooJjrZ9eLpoQ0
# QPJhyUBoMgYtc/0zPrW1l2khNgzu3eeGCBQt6aVAN7vfCna0awZU5WsG5+PH4ndy
# L27Fuutn4ijQomBAgTyx8PsMwy8r8X+o3kCNVIY4xRGsazi9I0ODSpec4ePfwjjz
# e+ZmPZUXx8t8vMrh+Qu/wGZ8Lw==
# SIG # End signature block
