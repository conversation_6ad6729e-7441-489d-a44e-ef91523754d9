#<#
function Get-TCCUserRoleInfo {
# <>
# ---------------------------------------------------- #
# Get-TCCUserRoleInfo                                  #
# Version 1.3  17-04-2024                              #
# Retrives users basic acc info and group memberships  #
# Alias: gur                                           #
# Usage: gur abc                                       #
# ---------------------------------------------------- #
# </>
    param (
        [Parameter(Mandatory = $true)]
        [string]$UserName
    )
    #Clear-Host
    # Retrieve the user object
    $line =  "----------------------------------------------------"
    $lined = "===================================================="

    try {
    $admin_account = "a_" + $UserName
    $user = Get-ADUser -Identity $UserName -Properties DisplayName, MemberOf
    $a_user = Get-ADUser -Filter { SamAccountName -eq $admin_account }
    $groups_count = (Get-ADUser $UserName -Properties MemberOf).MemberOf.count
    $empnum = Get-ADUser -Identity $UserName -Properties employeeNumber | Select-Object -ExpandProperty employeeNumber
    $emp_pos = (Get-ADUser -Filter { SamAccountName -eq $Username } -Properties extensionAttribute9).extensionAttribute9
    $ExDate = Get-ADUser -Identity $UserName -Properties AccountExpirationDate | Select-Object -ExpandProperty AccountExpirationDate | ForEach-Object {$_.ToString('D')}

    #$accstatus = Get-ADUser -Identity $UserName | Select-Object -Property Enabled | Format-Table -HideTableHeaders
    $accstatus = (Get-ADUser -Identity $UserName | Select-Object -Property Enabled | Format-Table -HideTableHeaders | Out-String).Trim()
    #$adminstatus = (Get-ADUser -Identity $admin_account | Select-Object -Property Enabled | Format-Table -HideTableHeaders | Out-String).Trim()

    # Display the user's display name property
    Write-Output $line
    Write-host "User Name: " -NoNewline
    write-Host "$($user.DisplayName) " -ForegroundColor Cyan -NoNewline
    write-Host "`tID: " -ForegroundColor white -NoNewline
    # write-host "'" -ForegroundColor cyan -NoNewline
    write-host "$UserName" -ForegroundColor green
    # write-host "' " -ForegroundColor cyan
    Write-Output $line
    write-host "Emp Num: " -ForegroundColor white -NoNewline
    
    if([string]::IsNullOrEmpty($empnum))
    {
        Write-Host "NO EMP Num" -ForegroundColor Red -NoNewline
    }
    else
    {
        write-Host "$empnum" -ForegroundColor cyan -NoNewline
    }

    Write-Host "`t`tPosition: " -ForegroundColor White -NoNewline
    Write-Host "$emp_pos" -ForegroundColor cyan 

    Write-host $line 


    write-Host "Acc Exp set to: " -ForegroundColor white -NoNewline
    if([string]::IsNullOrEmpty($ExDate))
    {
        Write-Host "NO Expire" -ForegroundColor magenta
    }
    else
    {
        write-Host $ExDate -ForegroundColor magenta
    }
    # Write-Output $line
    Write-host $line 
    write-host "Account status: "  -ForegroundColor white -NoNewline
    # $status = Get-Status $accstatus 
    # Write-host $status

    if ($accstatus -eq "False")
    {
        Write-Host "DISSABLED" -ForegroundColor red
    }
    elseif ($accstatus -eq "True")
    {
        Write-Host "ENABLED" -ForegroundColor green
    }

    Write-Output $line

    if ($a_user) {

        $adminstatus = (Get-ADUser -Identity $admin_account | Select-Object -Property Enabled | Format-Table -HideTableHeaders | Out-String).Trim()
        $adminExDate = Get-ADUser -Identity $admin_account -Properties AccountExpirationDate | Select-Object -ExpandProperty AccountExpirationDate | ForEach-Object {$_.ToString('D')}
        Write-Host " "
        Write-host $lined -ForegroundColor Red 
        Write-host "  User has an Admin account " -ForegroundColor yellow -NoNewline
        write-host "$admin_account" -ForegroundColor red -NoNewline
        write-host " "
        Write-host $line -ForegroundColor Red 

        # $a_status = Get-Status $adminstatus
        write-Host "  Acc Exp set to: " -ForegroundColor yellow -NoNewline
        if([string]::IsNullOrEmpty($adminExDate))
        {
            Write-Host "NO Expire" -ForegroundColor magenta
        }
        else
        {
            write-Host $adminExDate -ForegroundColor magenta
        }
        Write-host $line -ForegroundColor Red 
        write-host "  Admin ACC Status: " -ForegroundColor Yellow -NoNewline
        if ( $adminstatus -eq "False")
            {
                Write-Host "DISSABLED" -ForegroundColor red
            }
            elseif ($accstatus -eq "True")
            {
                Write-Host "ENABLED" -ForegroundColor green
            }
        #write-host $adminstatus -ForegroundColor Red
        Write-host $lined -ForegroundColor Red 
    } else {
        # Write-Output "The user '$admin_account' does not exist."
    }

    Write-output ''
    # Retrieve the groups that the user belongs to
    $groups = $user.MemberOf | ForEach-Object { Get-ADGroup $_ } | Sort-Object

    # Display the group names
    Write-host "User is a member of the following groups (" -ForegroundColor white -NoNewline
    Write-host "$groups_count" -ForegroundColor green -NoNewline
    Write-host "):" -ForegroundColor white
    Write-host ''  
    if (-not ($groups.Name -like "GG-ROLE*")) {
        Write-Host "- GG-ROLE* NOT assigned!!" -ForegroundColor Red
    }
    if (-not ($groups.Name -like "GG-ORG*")) {
        Write-Host "- GG-ORG* not found O: drive NOT assigned!!" -ForegroundColor Red
    }
    $groups | ForEach-Object {
        if ($_.Name -like "GG-ROLE*") {
            Write-Host "Role    : " -ForegroundColor Magenta -NoNewline
            Write-Host "$($_.Name)" -ForegroundColor Green
        } elseif ($_.Name -like "GG-ORG*") {
            Write-Host "O Drive : " -ForegroundColor Magenta -NoNewline
            Write-Host "$($_.Name)" -ForegroundColor Green
        } elseif ($_.Name -like "GG-APP*") {
            Write-Host "- $($_.Name)" -ForegroundColor yellow
#        } elseif ($_.Name -like "Group-*") {
#            Write-Host "- $($_.Name)" -ForegroundColor gray
        } else {
            Write-Output "- $($_.Name)"
        }
    }

    Write-Output ""
    Write-Output ""
    } catch {
        Write-Warning "Oops the user '$UserName' was not found!"
    }
}

Set-Alias gur Get-TCCUserRoleInfo
##>