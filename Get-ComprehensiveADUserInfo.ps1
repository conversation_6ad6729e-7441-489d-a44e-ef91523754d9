function Get-ComprehensiveADUserInfo {
    <#
    .SYNOPSIS
    Comprehensive Active Directory user information gathering tool
    
    .DESCRIPTION
    Retrieves extensive user information from Active Directory including identity, security, 
    contact details, organizational info, group memberships, and technical configuration.
    Version: 1.0
    Date: 2025-01-15
    
    .PARAMETER UserName
    The SamAccountName of the user to query
    
    .PARAMETER ShowAllProperties
    Display all available properties (verbose output)
    
    .PARAMETER ExportToFile
    Export results to a text file
    
    .PARAMETER FilePath
    Path for exported file (default: username_info.txt)
    
    .EXAMPLE
    Get-ComprehensiveADUserInfo -UserName "jsmith"
    
    .EXAMPLE
    Get-ComprehensiveADUserInfo -UserName "jsmith" -ShowAllProperties
    
    .EXAMPLE
    Get-ComprehensiveADUserInfo -UserName "jsmith" -ExportToFile -FilePath "C:\temp\jsmith_report.txt"
    
    .NOTES
    Alias: gcui
    Requires Active Directory PowerShell module
    #>
    
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$UserName,
        
        [Parameter(Mandatory = $false)]
        [switch]$ShowAllProperties,
        
        [Parameter(Mandatory = $false)]
        [switch]$ExportToFile,
        
        [Parameter(Mandatory = $false)]
        [string]$FilePath
    )
    
    # Define comprehensive property list
    $CoreProperties = @(
        'DisplayName', 'GivenName', 'Surname', 'UserPrincipalName', 'DistinguishedName',
        'Enabled', 'LockedOut', 'PasswordExpired', 'PasswordLastSet', 'PasswordNeverExpires',
        'CannotChangePassword', 'AccountExpirationDate', 'BadLogonCount', 'LastBadPasswordAttempt',
        'LastLogonDate', 'Created', 'Modified', 'WhenChanged', 'LogonCount',
        'EmailAddress', 'telephoneNumber', 'mobile', 'homePhone', 'otherTelephone',
        'facsimileTelephoneNumber', 'pager', 'ipPhone',
        'StreetAddress', 'City', 'State', 'PostalCode', 'Country', 'physicalDeliveryOfficeName',
        'Manager', 'DirectReports', 'Department', 'Company', 'Title', 'employeeID',
        'employeeNumber', 'employeeType', 'Division', 'Office',
        'MemberOf', 'PrimaryGroup', 'Description', 'Info', 'Comment',
        'HomeDirectory', 'HomeDrive', 'ProfilePath', 'ScriptPath', 'UserWorkstations',
        'extensionAttribute1', 'extensionAttribute2', 'extensionAttribute3', 'extensionAttribute4',
        'extensionAttribute5', 'extensionAttribute6', 'extensionAttribute7', 'extensionAttribute8',
        'extensionAttribute9', 'extensionAttribute10', 'extensionAttribute11', 'extensionAttribute12',
        'extensionAttribute13', 'extensionAttribute14', 'extensionAttribute15'
    )
    
    try {
        Write-Host "Gathering comprehensive AD user information for: " -NoNewline
        Write-Host "$UserName" -ForegroundColor Cyan
        Write-Host "=" * 80 -ForegroundColor Yellow
        
        # Get user with comprehensive properties
        if ($ShowAllProperties) {
            $user = Get-ADUser -Identity $UserName -Properties * -ErrorAction Stop
        } else {
            $user = Get-ADUser -Identity $UserName -Properties $CoreProperties -ErrorAction Stop
        }
        
        # Start building output
        $output = @()
        $output += "COMPREHENSIVE AD USER INFORMATION REPORT"
        $output += "Generated: $(Get-Date)"
        $output += "User: $UserName"
        $output += "=" * 80
        
        # Display and collect core identity information
        Show-CoreIdentity -User $user -Output ([ref]$output)
        
        # Display and collect account security information
        Show-AccountSecurity -User $user -Output ([ref]$output)
        
        # Display and collect login activity
        Show-LoginActivity -User $user -Output ([ref]$output)
        
        # Display and collect contact information
        Show-ContactInfo -User $user -Output ([ref]$output)
        
        # Display and collect organizational information
        Show-OrganizationalInfo -User $user -Output ([ref]$output)
        
        # Display and collect group memberships
        Show-GroupMemberships -User $user -Output ([ref]$output)
        
        # Display and collect technical configuration
        Show-TechnicalConfig -User $user -Output ([ref]$output)
        
        # Display and collect extended attributes
        Show-ExtendedAttributes -User $user -Output ([ref]$output)
        
        # Show all properties if requested
        if ($ShowAllProperties) {
            Show-AllProperties -User $user -Output ([ref]$output)
        }
        
        # Export to file if requested
        if ($ExportToFile) {
            if (-not $FilePath) {
                $FilePath = "${UserName}_comprehensive_info.txt"
            }
            $output | Out-File -FilePath $FilePath -Encoding UTF8
            Write-Host "`nReport exported to: " -NoNewline
            Write-Host "$FilePath" -ForegroundColor Green
        }
        
        Write-Host "`nReport completed successfully!" -ForegroundColor Green
        
    }
    catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
        Write-Warning "User '$UserName' was not found in Active Directory"
    }
    catch {
        Write-Error "An error occurred while retrieving user information: $($_.Exception.Message)"
    }
}

function Show-CoreIdentity {
    param($User, [ref]$Output)
    
    Write-Host "`nCORE IDENTITY INFORMATION" -ForegroundColor Yellow
    Write-Host "-" * 40
    
    $section = @()
    $section += "`nCORE IDENTITY INFORMATION"
    $section += "-" * 40
    
    $identityFields = @{
        'Display Name' = $User.DisplayName
        'Given Name' = $User.GivenName
        'Surname' = $User.Surname
        'Sam Account Name' = $User.SamAccountName
        'User Principal Name' = $User.UserPrincipalName
        'Distinguished Name' = $User.DistinguishedName
        'Object GUID' = $User.ObjectGUID
        'SID' = $User.SID
    }
    
    foreach ($field in $identityFields.GetEnumerator()) {
        $value = if ($field.Value) { $field.Value } else { "Not Set" }
        Write-Host "$($field.Key): " -NoNewline
        Write-Host "$value" -ForegroundColor Cyan
        $section += "$($field.Key): $value"
    }
    
    $Output.Value += $section
}

function Show-AccountSecurity {
    param($User, [ref]$Output)
    
    Write-Host "`nACCOUNT SECURITY & STATUS" -ForegroundColor Yellow
    Write-Host "-" * 40
    
    $section = @()
    $section += "`nACCOUNT SECURITY & STATUS"
    $section += "-" * 40
    
    # Account status with color coding
    Write-Host "Account Enabled: " -NoNewline
    if ($User.Enabled) {
        Write-Host "TRUE" -ForegroundColor Green
    } else {
        Write-Host "FALSE" -ForegroundColor Red
    }
    $section += "Account Enabled: $($User.Enabled)"
    
    Write-Host "Account Locked: " -NoNewline
    if ($User.LockedOut) {
        Write-Host "TRUE" -ForegroundColor Red
    } else {
        Write-Host "FALSE" -ForegroundColor Green
    }
    $section += "Account Locked: $($User.LockedOut)"
    
    # Password information
    $passwordFields = @{
        'Password Expired' = $User.PasswordExpired
        'Password Last Set' = if ($User.PasswordLastSet) { $User.PasswordLastSet.ToString("yyyy-MMM-dd HH:mm") } else { "Never" }
        'Password Never Expires' = $User.PasswordNeverExpires
        'Cannot Change Password' = $User.CannotChangePassword
        'Account Expiration' = if ($User.AccountExpirationDate) { $User.AccountExpirationDate.ToString("yyyy-MMM-dd") } else { "Never" }
        'Bad Logon Count' = $User.BadLogonCount
        'Last Bad Password Attempt' = if ($User.LastBadPasswordAttempt) { $User.LastBadPasswordAttempt.ToString("yyyy-MMM-dd HH:mm") } else { "Never" }
    }
    
    foreach ($field in $passwordFields.GetEnumerator()) {
        Write-Host "$($field.Key): " -NoNewline
        Write-Host "$($field.Value)" -ForegroundColor Cyan
        $section += "$($field.Key): $($field.Value)"
    }
    
    $Output.Value += $section
}

function Show-LoginActivity {
    param($User, [ref]$Output)
    
    Write-Host "`nLOGIN ACTIVITY" -ForegroundColor Yellow
    Write-Host "-" * 40
    
    $section = @()
    $section += "`nLOGIN ACTIVITY"
    $section += "-" * 40
    
    # Account creation
    Write-Host "Account Created: " -NoNewline
    if ($User.Created) {
        Write-Host $User.Created.ToString("yyyy-MMM-dd HH:mm") -ForegroundColor Cyan
        $section += "Account Created: $($User.Created.ToString('yyyy-MMM-dd HH:mm'))"
    } else {
        Write-Host "Unknown" -ForegroundColor Red
        $section += "Account Created: Unknown"
    }
    
    # Last logon with days calculation
    Write-Host "Last Logon: " -NoNewline
    if ($User.LastLogonDate) {
        $daysSinceLogon = (Get-Date) - $User.LastLogonDate
        Write-Host $User.LastLogonDate.ToString("yyyy-MMM-dd HH:mm") -ForegroundColor Cyan -NoNewline
        Write-Host " (" -NoNewline
        Write-Host "$([math]::Round($daysSinceLogon.TotalDays)) days ago" -ForegroundColor Yellow -NoNewline
        Write-Host ")"
        $section += "Last Logon: $($User.LastLogonDate.ToString('yyyy-MMM-dd HH:mm')) ($([math]::Round($daysSinceLogon.TotalDays)) days ago)"
    } else {
        Write-Host "Never" -ForegroundColor Red
        $section += "Last Logon: Never"
    }
    
    # Additional activity info
    $activityFields = @{
        'Logon Count' = if ($User.LogonCount) { $User.LogonCount } else { "0" }
        'Last Modified' = if ($User.Modified) { $User.Modified.ToString("yyyy-MMM-dd HH:mm") } else { "Unknown" }
        'When Changed' = if ($User.WhenChanged) { $User.WhenChanged.ToString("yyyy-MMM-dd HH:mm") } else { "Unknown" }
    }
    
    foreach ($field in $activityFields.GetEnumerator()) {
        Write-Host "$($field.Key): " -NoNewline
        Write-Host "$($field.Value)" -ForegroundColor Cyan
        $section += "$($field.Key): $($field.Value)"
    }
    
    $Output.Value += $section
}
